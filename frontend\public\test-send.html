<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发送消息测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .input-group {
            margin: 10px 0;
        }
        input, button {
            padding: 10px;
            margin: 5px;
            font-size: 16px;
        }
        input {
            width: 300px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 3px;
        }
        .status.sending {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>数字人发送消息测试</h1>
    
    <div class="test-container">
        <h3>发送状态测试</h3>
        <div class="input-group">
            <input type="text" id="messageInput" placeholder="输入测试消息" value="你好">
            <button id="sendBtn" onclick="testSend()">发送消息</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="status" class="status">就绪</div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let isSending = false;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(text, type = '') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = text;
            statusDiv.className = 'status ' + type;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function testSend() {
            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const message = messageInput.value.trim();
            
            log(`🚀 尝试发送消息: "${message}"`);
            log(`📊 当前发送状态: ${isSending}`);
            
            if (!message) {
                log('❌ 消息为空');
                return;
            }
            
            if (isSending) {
                log('❌ 正在发送中，跳过本次请求');
                return;
            }
            
            // 设置发送状态
            isSending = true;
            sendBtn.disabled = true;
            updateStatus('发送中...', 'sending');
            log('✅ 设置发送状态为 true');
            
            try {
                // 模拟API调用
                log('📡 开始API调用...');
                
                const response = await fetch('/api/v1/digital-human/71/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer demo_token'
                    },
                    body: JSON.stringify({ message: message })
                });
                
                log(`📡 API响应状态: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`✅ API调用成功: ${JSON.stringify(data, null, 2)}`);
                
                updateStatus('发送成功', 'success');
                
                // 模拟处理时间
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                log(`❌ API调用失败: ${error.message}`);
                updateStatus('发送失败', 'error');
            } finally {
                // 重置发送状态
                isSending = false;
                sendBtn.disabled = false;
                updateStatus('就绪', '');
                log('🔄 重置发送状态为 false');
                log('---');
            }
        }
        
        // 回车发送
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testSend();
            }
        });
        
        log('🎯 测试页面已加载');
    </script>
</body>
</html>
