/**
 * 资源路径工具
 */

export function getAssetPath(path) {
  return new URL(`../assets/${path}`, import.meta.url).href
}

export function getImagePath(imageName) {
  return getAssetPath(`images/${imageName}`)
}

export function getIconPath(iconName) {
  return getAssetPath(`icons/${iconName}`)
}

// 获取静态资源URL (DigitalHumanApp.vue需要)
export function getStaticResourceUrl(path) {
  if (!path) return ''

  // 如果是完整的HTTP/HTTPS URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path
  }

  // 如果是协议相对URL，直接返回
  if (path.startsWith('//')) {
    return path
  }

  // 处理以/开头的路径，这些应该是public目录下的静态资源
  if (path.startsWith('/')) {
    // 确保路径指向public目录下的资源，而不是API路径
    return path
  }

  // 处理相对路径
  return `/${path.replace(/^\.?\//, '')}`
}

// 获取公共资源URL
export function getPublicResourceUrl(path) {
  return getStaticResourceUrl(path)
}

// 获取媒体资源URL
export function getMediaResourceUrl(path) {
  return getStaticResourceUrl(path)
}

// 解析资源URL
export function resolveAssetUrl(path) {
  return getStaticResourceUrl(path)
}

export default {
  getAssetPath,
  getImagePath,
  getIconPath
}
