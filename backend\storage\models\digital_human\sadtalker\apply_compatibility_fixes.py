#!/usr/bin/env python3
"""
Apply compatibility fixes for SadTalker before running inference
This script should be run before any SadTalker operations
"""

import sys
import os

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """Apply all necessary compatibility fixes"""
    print("Applying compatibility fixes for SadTalker...")
    
    try:
        # Import and apply torchvision compatibility fix
        from torchvision_compatibility_fix import patch_basicsr_imports
        
        success = patch_basicsr_imports()
        if success:
            print("✅ Torchvision compatibility fix applied successfully")
        else:
            print("❌ Failed to apply torchvision compatibility fix")
            return False
            
        # Additional fixes can be added here
        
        print("✅ All compatibility fixes applied successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error applying compatibility fixes: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
