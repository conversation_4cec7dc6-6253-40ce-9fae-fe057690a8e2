"""
数字人API路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from typing import List, Optional
from pydantic import BaseModel
from ...core.database import get_db_manager
from ...services.digital_human_service import DigitalHumanService

router = APIRouter()
digital_human_service = DigitalHumanService()


class DigitalHumanCreate(BaseModel):
    """创建数字人请求模型"""
    name: str
    description: Optional[str] = None
    voice_id: str = "default"
    model_type: str = "default"


class DigitalHumanResponse(BaseModel):
    """数字人响应模型"""
    id: int
    name: str
    description: Optional[str]
    avatar_path: Optional[str]
    voice_id: str
    model_type: str
    status: str
    created_at: str


@router.get("/list")
async def get_digital_human_list(
    page: int = 1,
    page_size: int = 12,
    category: str = "all"
):
    """获取数字人列表（支持分页）"""
    try:
        print(f"[Digital Human API] 获取数字人列表 - 页码: {page}, 每页: {page_size}, 分类: {category}")

        # 计算偏移量
        offset = (page - 1) * page_size

        # 构建查询条件
        where_clause = ""
        params = []

        if category != "all":
            where_clause = "WHERE type = ?"
            params.append(category)

        db_manager = get_db_manager()

        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM digital_humans {where_clause}"
        total_result = db_manager.execute_query(count_query, params)
        total = total_result[0]["total"] if total_result else 0

        # 获取分页数据
        data_query = f"""
            SELECT * FROM digital_humans
            {where_clause}
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """
        data_params = params + [page_size, offset]
        digital_humans = db_manager.execute_query(data_query, data_params)

        print(f"[Digital Human API] 查询到 {len(digital_humans or [])} 个数字人，总数: {total}")

        return {
            "success": True,
            "digitalHumans": [
                {
                    "id": dh["id"],
                    "digital_human_id": dh.get("digital_human_id"),
                    "name": dh["name"],
                    "description": dh.get("description", ""),
                    "type": dh.get("type", "assistant"),  # 统一使用type字段
                    "gender": dh.get("gender"),
                    "avatar_path": dh.get("generated_image_url") or dh.get("uploaded_image_url"),
                    "avatar_url": dh.get("generated_image_url") or dh.get("uploaded_image_url"),
                    "thumbnail_url": dh.get("thumbnail_url"),
                    "preview_video_url": dh.get("preview_video_url"),  # 添加视频URL
                    "video_url": dh.get("preview_video_url"),  # 前端兼容性
                    "model_url": dh.get("model_url"),  # 3D模型URL
                    "voice_id": dh.get("voice_id"),
                    "voice_type": dh.get("voiceType"),
                    "voice_config_url": dh.get("voice_config_url"),  # 语音配置URL
                    "model_type": dh.get("type", "assistant"),  # 保持兼容性
                    "status": dh.get("generation_status", "unknown"),
                    "generation_status": dh.get("generation_status"),
                    "generation_progress": dh.get("generation_progress", 0),
                    "usage_count": dh.get("usage_count", 0),  # 使用次数
                    "style": dh.get("style"),
                    "created_at": str(dh["created_at"]) if dh.get("created_at") else None,
                    "updated_at": str(dh["updated_at"]) if dh.get("updated_at") else None
                }
                for dh in (digital_humans or [])
            ],
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "total_pages": (total + page_size - 1) // page_size
            }
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "digitalHumans": []
        }


@router.post("/create")
async def create_digital_human(
    name: str = Form(...),
    description: str = Form(None),
    voice_id: str = Form("default"),
    model_type: str = Form("default"),
    avatar: UploadFile = File(None)
):
    """创建数字人"""
    try:
        # 保存头像文件
        avatar_path = None
        if avatar:
            avatar_path = await digital_human_service.save_avatar(avatar)
        
        # 保存到数据库
        db_manager = get_db_manager()
        result = db_manager.execute_query(
            """
            INSERT INTO digital_humans (name, description, avatar_path, voice_id, model_type, status)
            VALUES (?, ?, ?, ?, ?, ?)
            """,
            (name, description, avatar_path, voice_id, model_type, "ready")
        )
        
        if result:
            # 获取新创建的数字人信息
            new_digital_human = db_manager.execute_query(
                "SELECT * FROM digital_humans WHERE rowid = last_insert_rowid()"
            )
            
            if new_digital_human:
                dh = new_digital_human[0]
                return {
                    "success": True,
                    "digitalHuman": {
                        "id": dh["id"],
                        "name": dh["name"],
                        "description": dh["description"],
                        "avatar_path": dh["avatar_path"],
                        "voice_id": dh["voice_id"],
                        "model_type": dh["model_type"],
                        "status": dh["status"],
                        "created_at": dh["created_at"]
                    }
                }
        
        return {"success": False, "error": "创建失败"}
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.delete("/{digital_human_id}")
async def delete_digital_human(digital_human_id: int):
    """删除数字人"""
    try:
        db_manager = get_db_manager()
        result = db_manager.execute_query(
            "DELETE FROM digital_humans WHERE id = ?",
            (digital_human_id,)
        )
        
        if result > 0:
            return {"success": True}
        else:
            return {"success": False, "error": "数字人不存在"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/{digital_human_id}")
async def get_digital_human_detail(digital_human_id: int):
    """获取数字人详情"""
    try:
        db_manager = get_db_manager()
        digital_human = db_manager.execute_query(
            "SELECT * FROM digital_humans WHERE id = ?",
            (digital_human_id,)
        )
        
        if digital_human:
            dh = digital_human[0]
            return {
                "success": True,
                "digitalHuman": {
                    "id": dh["id"],
                    "name": dh["name"],
                    "type": dh.get("type"),
                    "gender": dh.get("gender"),
                    "description": dh.get("description"),
                    "welcome_text": dh.get("welcome_text"),
                    "appearance_type": dh.get("appearance_type"),
                    "selected_template": dh.get("selected_template"),
                    "avatar_url": dh.get("generated_image_url") or dh.get("uploaded_image_url") or dh.get("avatar_url"),
                    "avatar_video_url": dh.get("avatar_video_url"),
                    "video_url": dh.get("avatar_video_url"),  # 前端兼容性
                    "thumbnail_url": dh.get("thumbnail_url"),
                    "voice_sample_url": dh.get("voice_sample_url"),
                    "status": dh.get("status"),
                    "progress": dh.get("progress", 0),
                    "created_at": dh.get("created_at"),
                    "updated_at": dh.get("updated_at")
                }
            }
        else:
            return {"success": False, "error": "数字人不存在"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/{digital_human_id}/chat")
async def chat_with_digital_human(digital_human_id: int, message: dict):
    """与数字人对话"""
    try:
        user_message = message.get("message", "")
        if not user_message:
            return {"success": False, "error": "消息不能为空"}

        # 尝试使用Ollama服务
        try:
            from ...services.ollama_service import OllamaService
            ollama_service = OllamaService()

            if ollama_service.available and ollama_service.best_model:
                # 构建数字人角色提示
                system_prompt = f"你是数字人助手{digital_human_id}，请用友好、专业的语气回复用户。"

                # 使用Ollama生成回复
                response = ollama_service.chat_completion(
                    model=ollama_service.best_model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_message}
                    ],
                    temperature=0.7,
                    max_tokens=1000
                )

                if response:
                    return {
                        "success": True,
                        "response": response,
                        "model": ollama_service.best_model
                    }
        except Exception as ollama_error:
            print(f"Ollama服务失败: {ollama_error}")

        # 回退到默认回复
        response = f"我收到了您的消息「{user_message}」，很高兴为您服务！"

        return {
            "success": True,
            "response": response,
            "model": "default"
        }

    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/models")
async def get_available_models():
    """获取可用模型列表"""
    try:
        from ...services.ollama_service import OllamaService
        ollama_service = OllamaService()

        models = [
            {"id": "default", "name": "默认模型", "status": "available"}
        ]

        if ollama_service.available:
            ollama_models = ollama_service.list_models()
            for model in ollama_models:
                # model是字典对象，包含name等字段
                model_name = model.get('name', str(model)) if isinstance(model, dict) else str(model)
                models.append({
                    "id": model_name,
                    "name": f"Ollama - {model_name}",
                    "status": "available",
                    "type": "ollama"
                })

        return {
            "success": True,
            "models": models
        }
    except Exception as e:
        return {
            "success": True,
            "models": [
                {"id": "default", "name": "默认模型", "status": "available"}
            ]
        }


@router.get("/voices")
async def get_voice_list():
    """获取语音列表"""
    return {
        "success": True,
        "voices": [
            {"id": "female1", "name": "女声1", "language": "zh-CN"},
            {"id": "male1", "name": "男声1", "language": "zh-CN"},
            {"id": "female2", "name": "女声2", "language": "en-US"},
            {"id": "male2", "name": "男声2", "language": "en-US"}
        ]
    }
