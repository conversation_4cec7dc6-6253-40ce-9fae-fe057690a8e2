#!/usr/bin/env python3
"""
Download essential LivePortrait model files
"""

import os
import sys
from pathlib import Path
from huggingface_hub import hf_hub_download
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def download_essential_models():
    """Download only the essential model files needed for LivePortrait"""
    
    script_dir = Path(__file__).parent
    pretrained_weights_dir = script_dir / "pretrained_weights"
    
    # Create directory structure
    base_models_dir = pretrained_weights_dir / "liveportrait" / "base_models"
    base_models_dir.mkdir(parents=True, exist_ok=True)
    
    # Essential model files to download
    essential_files = [
        "liveportrait/base_models/appearance_feature_extractor.pth",
        "liveportrait/base_models/motion_extractor.pth", 
        "liveportrait/base_models/warping_module.pth",
        "liveportrait/base_models/spade_generator.pth",
        "liveportrait/base_models/stitching_retargeting_module.pth"
    ]
    
    repo_id = "KwaiVGI/LivePortrait"
    
    for file_path in essential_files:
        try:
            logger.info(f"Downloading {file_path}...")
            
            # Download the file
            downloaded_path = hf_hub_download(
                repo_id=repo_id,
                filename=file_path,
                local_dir=pretrained_weights_dir,
                local_dir_use_symlinks=False
            )
            
            logger.info(f"Successfully downloaded: {downloaded_path}")
            
        except Exception as e:
            logger.error(f"Failed to download {file_path}: {e}")
            continue
    
    # Check if all essential files are present
    missing_files = []
    for file_path in essential_files:
        full_path = pretrained_weights_dir / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.warning(f"Missing files: {missing_files}")
        return False
    else:
        logger.info("All essential model files downloaded successfully!")
        return True

if __name__ == "__main__":
    success = download_essential_models()
    sys.exit(0 if success else 1)
