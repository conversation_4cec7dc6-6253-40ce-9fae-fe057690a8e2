#!/usr/bin/env python3
"""
更新数字人记录的生成结果
根据日志中的信息更新数字人71的生成结果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import get_db_manager
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_digital_human_71():
    """更新数字人71的生成结果"""
    
    db_manager = get_db_manager()
    
    # 根据日志中的信息更新数字人71
    digital_human_id = 71
    
    # 从日志中获取的生成结果
    result_urls = {
        'avatar_url': '/storage/digital_humans/avatar_71_20250730_164108.jpg',
        'model_url': '/storage/digital_humans/model_71_20250730_164114.glb', 
        'preview_video_url': '/storage/digital_humans/preview_71_20250730_164114.mp4',
        'voice_config_url': '/storage/digital_humans/voice_config_71.json'
    }
    
    try:
        logger.info(f"更新数字人 {digital_human_id} 的生成结果...")
        
        # 更新数字人记录
        update_sql = """
            UPDATE digital_humans
            SET
                generated_image_url = %s,
                model_url = %s,
                preview_video_url = %s,
                voice_config_url = %s,
                generation_status = 'completed',
                generation_progress = 100,
                status = 'ready',
                updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """

        db_manager.execute_query(update_sql, (
            result_urls['avatar_url'],  # 存储到generated_image_url字段
            result_urls['model_url'],
            result_urls['preview_video_url'],
            result_urls['voice_config_url'],
            digital_human_id
        ))
        
        logger.info(f"✅ 数字人 {digital_human_id} 更新成功")
        
        # 验证更新结果
        verify_sql = "SELECT id, name, generated_image_url, model_url, preview_video_url, voice_config_url, generation_status FROM digital_humans WHERE id = %s"
        result = db_manager.execute_query(verify_sql, (digital_human_id,))
        
        if result:
            dh = result[0]
            logger.info("更新后的数字人信息:")
            logger.info(f"  ID: {dh['id']}")
            logger.info(f"  名称: {dh['name']}")
            logger.info(f"  头像URL: {dh['generated_image_url']}")
            logger.info(f"  模型URL: {dh['model_url']}")
            logger.info(f"  视频URL: {dh['preview_video_url']}")
            logger.info(f"  语音配置URL: {dh['voice_config_url']}")
            logger.info(f"  生成状态: {dh['generation_status']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 更新数字人 {digital_human_id} 失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("更新数字人生成结果脚本")
    logger.info("=" * 60)
    
    try:
        if update_digital_human_71():
            logger.info("✅ 数字人生成结果更新成功！")
            return True
        else:
            logger.error("❌ 数字人生成结果更新失败")
            return False
            
    except Exception as e:
        logger.error(f"更新过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
