/**
 * 资源工具函数
 * 处理静态资源URL的获取和转换
 */

/**
 * 获取静态资源的完整URL
 * @param {string} path - 资源路径
 * @returns {string} 完整的资源URL
 */
export function getStaticResourceUrl(path) {
  if (!path) return ''

  // 如果已经是完整URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path
  }

  // 确保路径以/开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`

  // 对于静态资源（如图片、SVG等），直接返回路径，让浏览器从public目录加载
  // 这些资源不需要通过API服务器
  if (normalizedPath.match(/\.(svg|png|jpg|jpeg|gif|ico|css|js)$/i)) {
    return normalizedPath
  }

  // 对于其他资源（如存储的文件），使用API基础URL
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
  return `${baseURL}${normalizedPath}`
}

/**
 * 获取数字人头像URL
 * @param {string} avatarPath - 头像路径
 * @returns {string} 头像URL
 */
export function getDigitalHumanAvatarUrl(avatarPath) {
  return getStaticResourceUrl(avatarPath)
}

/**
 * 获取数字人视频URL
 * @param {string} videoPath - 视频路径
 * @returns {string} 视频URL
 */
export function getDigitalHumanVideoUrl(videoPath) {
  return getStaticResourceUrl(videoPath)
}

/**
 * 获取数字人模型URL
 * @param {string} modelPath - 模型路径
 * @returns {string} 模型URL
 */
export function getDigitalHumanModelUrl(modelPath) {
  return getStaticResourceUrl(modelPath)
}

/**
 * 检查资源是否可访问
 * @param {string} url - 资源URL
 * @returns {Promise<boolean>} 是否可访问
 */
export async function checkResourceAvailable(url) {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch (error) {
    console.warn('资源检查失败:', url, error)
    return false
  }
}

/**
 * 获取文件扩展名
 * @param {string} filename - 文件名
 * @returns {string} 扩展名
 */
export function getFileExtension(filename) {
  if (!filename) return ''
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : ''
}

/**
 * 判断是否为图片文件
 * @param {string} filename - 文件名
 * @returns {boolean} 是否为图片
 */
export function isImageFile(filename) {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
  const ext = getFileExtension(filename)
  return imageExtensions.includes(ext)
}

/**
 * 判断是否为视频文件
 * @param {string} filename - 文件名
 * @returns {boolean} 是否为视频
 */
export function isVideoFile(filename) {
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv']
  const ext = getFileExtension(filename)
  return videoExtensions.includes(ext)
}

/**
 * 判断是否为音频文件
 * @param {string} filename - 文件名
 * @returns {boolean} 是否为音频
 */
export function isAudioFile(filename) {
  const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
  const ext = getFileExtension(filename)
  return audioExtensions.includes(ext)
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 生成缩略图URL
 * @param {string} originalUrl - 原始URL
 * @param {number} width - 宽度
 * @param {number} height - 高度
 * @returns {string} 缩略图URL
 */
export function getThumbnailUrl(originalUrl, width = 200, height = 200) {
  if (!originalUrl) return ''
  
  // 如果是本地资源，可以添加缩略图参数
  if (originalUrl.includes('/storage/')) {
    const separator = originalUrl.includes('?') ? '&' : '?'
    return `${originalUrl}${separator}w=${width}&h=${height}&fit=cover`
  }
  
  return originalUrl
}

export default {
  getStaticResourceUrl,
  getDigitalHumanAvatarUrl,
  getDigitalHumanVideoUrl,
  getDigitalHumanModelUrl,
  checkResourceAvailable,
  getFileExtension,
  isImageFile,
  isVideoFile,
  isAudioFile,
  formatFileSize,
  getThumbnailUrl
}
