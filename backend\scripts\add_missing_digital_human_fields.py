#!/usr/bin/env python3
"""
为数字人表添加缺失的字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import get_db_manager
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_missing_fields():
    """为数字人表添加缺失的字段"""
    
    db_manager = get_db_manager()
    
    # 需要添加的字段
    fields_to_add = [
        {
            'name': 'preview_video_url',
            'type': 'TEXT',
            'comment': '预览视频URL'
        },
        {
            'name': 'model_url', 
            'type': 'TEXT',
            'comment': '3D模型URL'
        },
        {
            'name': 'voice_config_url',
            'type': 'TEXT', 
            'comment': '语音配置URL'
        },
        {
            'name': 'usage_count',
            'type': 'INTEGER',
            'default': '0',
            'comment': '使用次数'
        },
        {
            'name': 'thumbnail_url',
            'type': 'TEXT',
            'comment': '缩略图URL'
        },
        {
            'name': 'generation_status',
            'type': 'TEXT',
            'comment': '生成状态'
        },
        {
            'name': 'generation_progress',
            'type': 'INTEGER',
            'default': '0',
            'comment': '生成进度'
        },
        {
            'name': 'type',
            'type': 'TEXT',
            'comment': '数字人类型'
        },
        {
            'name': 'gender',
            'type': 'TEXT',
            'comment': '性别'
        }
    ]
    
    logger.info("开始为数字人表添加缺失字段...")
    
    # 首先检查表结构
    try:
        # 获取现有字段 (PostgreSQL语法)
        existing_fields = db_manager.execute_query("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'digital_humans'
        """)
        existing_field_names = [field['column_name'] for field in existing_fields] if existing_fields else []

        logger.info(f"现有字段: {existing_field_names}")

        # 添加缺失的字段
        for field in fields_to_add:
            field_name = field['name']

            if field_name not in existing_field_names:
                try:
                    # 构建ALTER TABLE语句
                    alter_sql = f"ALTER TABLE digital_humans ADD COLUMN {field_name} {field['type']}"

                    if 'default' in field:
                        alter_sql += f" DEFAULT {field['default']}"

                    logger.info(f"添加字段: {field_name}")
                    db_manager.execute_query(alter_sql)
                    logger.info(f"✅ 成功添加字段: {field_name}")

                except Exception as e:
                    logger.error(f"❌ 添加字段 {field_name} 失败: {e}")
            else:
                logger.info(f"⏭️  字段 {field_name} 已存在，跳过")

        # 验证字段添加结果
        updated_fields = db_manager.execute_query("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'digital_humans'
        """)
        updated_field_names = [field['column_name'] for field in updated_fields] if updated_fields else []
        
        logger.info(f"更新后的字段: {updated_field_names}")
        
        # 检查是否所有字段都添加成功
        missing_fields = [field['name'] for field in fields_to_add if field['name'] not in updated_field_names]
        
        if missing_fields:
            logger.warning(f"以下字段仍然缺失: {missing_fields}")
        else:
            logger.info("✅ 所有字段都已成功添加")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 添加字段过程中出错: {e}")
        return False

def update_existing_records():
    """更新现有记录的默认值"""
    
    db_manager = get_db_manager()
    
    try:
        logger.info("更新现有记录的默认值...")
        
        # 更新usage_count为0（如果为NULL）
        db_manager.execute_query("""
            UPDATE digital_humans 
            SET usage_count = 0 
            WHERE usage_count IS NULL
        """)
        
        # 更新generation_progress为0（如果为NULL）
        db_manager.execute_query("""
            UPDATE digital_humans 
            SET generation_progress = 0 
            WHERE generation_progress IS NULL
        """)
        
        # 设置默认类型为assistant（如果为NULL）
        db_manager.execute_query("""
            UPDATE digital_humans 
            SET type = 'assistant' 
            WHERE type IS NULL
        """)
        
        logger.info("✅ 现有记录默认值更新完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 更新现有记录失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("数字人表字段迁移脚本")
    logger.info("=" * 60)
    
    try:
        # 添加缺失字段
        if add_missing_fields():
            logger.info("字段添加完成")
            
            # 更新现有记录
            if update_existing_records():
                logger.info("记录更新完成")
                logger.info("✅ 数字人表迁移成功完成！")
                return True
            else:
                logger.error("记录更新失败")
                return False
        else:
            logger.error("字段添加失败")
            return False
            
    except Exception as e:
        logger.error(f"迁移过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
