#!/usr/bin/env python3
"""
MuseTalk专用任务 - 只生成说话视频，不生成新的数字人
"""

import os
import sys
import logging
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, Any

from app.core.celery_unified import celery_app
from app.tasks.base import BaseTask, TaskProgressTracker, task_with_progress

logger = logging.getLogger(__name__)

@celery_app.task(bind=True, base=BaseTask, name="generate_musetalk_video")
@task_with_progress("MuseTalk视频生成")
def generate_musetalk_video(self, task_id: str, tracker: TaskProgressTracker, 
                           digital_human_id: int, message: str, voice_id: str = None):
    """
    专门的MuseTalk视频生成任务
    
    Args:
        task_id: 任务ID
        tracker: 进度跟踪器
        digital_human_id: 数字人ID
        message: 要说的消息内容
        voice_id: 语音ID
    """
    
    try:
        tracker.update(5, "初始化MuseTalk视频生成...")
        logger.info(f"🎬 开始为数字人{digital_human_id}生成MuseTalk视频")
        logger.info(f"📝 消息内容: {message}")
        
        # 设置路径
        storage_dir = Path("storage/digital_humans")
        musetalk_dir = Path("storage/musetalk_videos")
        musetalk_dir.mkdir(exist_ok=True)
        
        # 查找现有头像
        tracker.update(10, "查找数字人头像...")
        avatar_path = None
        for ext in ['.jpg', '.jpeg', '.png']:
            potential_path = storage_dir / f"avatar_{digital_human_id}_20250730_164108{ext}"
            if potential_path.exists():
                avatar_path = potential_path
                break
        
        if not avatar_path:
            # 查找任何匹配的头像
            for avatar_file in storage_dir.glob(f"avatar_{digital_human_id}_*"):
                if avatar_file.suffix.lower() in ['.jpg', '.jpeg', '.png']:
                    avatar_path = avatar_file
                    break
        
        if not avatar_path:
            raise ValueError(f"找不到数字人{digital_human_id}的头像文件")
        
        logger.info(f"✅ 找到头像: {avatar_path}")
        
        # 生成音频
        tracker.update(20, "生成语音音频...")
        audio_path = _generate_audio_for_message(message, voice_id)
        if not audio_path or not Path(audio_path).exists():
            raise ValueError("音频生成失败")
        
        logger.info(f"✅ 音频生成成功: {audio_path}")
        
        # 生成输出路径
        output_path = musetalk_dir / f"{task_id}.mp4"
        
        # 使用简化的MuseTalk生成
        tracker.update(30, "启动MuseTalk视频生成...")
        success = _generate_musetalk_video_simple(avatar_path, audio_path, output_path, tracker)
        
        if success and output_path.exists():
            tracker.update(100, "MuseTalk视频生成完成!")
            logger.info(f"✅ MuseTalk视频生成成功: {output_path}")
            
            return {
                "success": True,
                "video_path": str(output_path),
                "video_url": f"/storage/musetalk_videos/{task_id}.mp4",
                "audio_path": str(audio_path),
                "message": message,
                "digital_human_id": digital_human_id
            }
        else:
            raise ValueError("MuseTalk视频生成失败")
            
    except Exception as e:
        logger.error(f"❌ MuseTalk视频生成失败: {e}")
        tracker.update(0, f"生成失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": message,
            "digital_human_id": digital_human_id
        }

def _generate_audio_for_message(message: str, voice_id: str = None) -> str:
    """为消息生成音频"""
    try:
        import pyttsx3
        from io import BytesIO
        
        # 生成音频文件路径
        audio_dir = Path("storage/musetalk_audio")
        audio_dir.mkdir(exist_ok=True)
        audio_path = audio_dir / f"audio_{hash(message)}.wav"
        
        # 先尝试使用gTTS（更稳定）
        try:
            from gtts import gTTS
            tts = gTTS(text=message, lang='zh-cn', slow=False)

            # 直接保存为wav格式（简化处理）
            temp_mp3 = audio_path.with_suffix('.mp3')
            tts.save(str(temp_mp3))

            # 检查文件是否生成成功
            if temp_mp3.exists() and temp_mp3.stat().st_size > 1000:
                # 重命名为wav（MuseTalk可以处理mp3）
                temp_mp3.rename(audio_path)
                logger.info(f"✅ gTTS音频生成成功: {audio_path}")
                return str(audio_path)

        except Exception as gtts_error:
            logger.warning(f"gTTS失败: {gtts_error}")
        
        # 回退到pyttsx3
        try:
            engine = pyttsx3.init()
            engine.setProperty('rate', 150)
            engine.setProperty('volume', 0.9)
            
            # 设置中文语音
            voices = engine.getProperty('voices')
            for voice in voices:
                if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                    engine.setProperty('voice', voice.id)
                    break
            
            engine.save_to_file(message, str(audio_path))
            engine.runAndWait()
            
            if audio_path.exists() and audio_path.stat().st_size > 1000:
                logger.info(f"✅ pyttsx3音频生成成功: {audio_path}")
                return str(audio_path)
                
        except Exception as pyttsx3_error:
            logger.warning(f"pyttsx3失败: {pyttsx3_error}")
        
        raise ValueError("所有TTS方法都失败了")
        
    except Exception as e:
        logger.error(f"❌ 音频生成失败: {e}")
        return None

def _generate_musetalk_video_simple(avatar_path: Path, audio_path: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """使用简化的方法生成MuseTalk视频"""
    try:
        tracker.update(40, "使用CPU版本MuseTalk...")
        
        # 使用成功的GPU版本（和创建数字人时一样）
        musetalk_script = Path("storage/models/digital_human/musetalk/gpu_musetalk_inference.py")

        if not musetalk_script.exists():
            logger.error(f"❌ 找不到MuseTalk脚本: {musetalk_script}")
            return False

        logger.info(f"✅ 找到MuseTalk脚本: {musetalk_script}")
        
        tracker.update(50, "执行MuseTalk生成...")
        
        # 构建命令（使用和创建数字人时相同的参数）
        cmd = [
            sys.executable,
            str(musetalk_script.absolute()),
            "--source_image", str(avatar_path.absolute()),
            "--driving_audio", str(Path(audio_path).absolute()),  # 使用绝对路径
            "--output", str(output_path.absolute()),
            "--device", "cuda",  # 使用GPU（和创建数字人时一样）
            "--fps", "25",
            "--quality", "high",  # 使用高质量
            "--bbox_shift", "0"
        ]
        
        logger.info(f"🚀 执行MuseTalk命令: {' '.join(cmd)}")
        
        # 执行命令（使用和创建数字人时相同的设置）
        result = subprocess.run(
            cmd,
            cwd=str(musetalk_script.parent),
            capture_output=True,
            text=True,
            timeout=120  # GPU: 2分钟超时（和创建数字人时一样）
        )
        
        tracker.update(90, "检查生成结果...")
        
        if result.returncode == 0 and output_path.exists():
            logger.info("✅ MuseTalk视频生成成功")
            return True
        else:
            logger.error(f"❌ MuseTalk执行失败: {result.stderr}")

            # 如果GPU版本失败，尝试使用简化版本作为回退
            logger.info("🔄 GPU版本失败，尝试使用简化版本...")
            return _generate_fallback_video(avatar_path, audio_path, output_path, tracker)
            
    except subprocess.TimeoutExpired:
        logger.error("❌ MuseTalk执行超时")
        return False
    except Exception as e:
        logger.error(f"❌ MuseTalk执行异常: {e}")
        return False

def _generate_fallback_video(avatar_path: Path, audio_path: str, output_path: Path, tracker: TaskProgressTracker) -> bool:
    """生成回退视频（简化版本）"""
    try:
        tracker.update(60, "使用回退方案生成视频...")

        import cv2
        import numpy as np
        from PIL import Image
        import subprocess

        logger.info("🎬 开始生成回退说话视频")

        # 读取头像
        img = Image.open(avatar_path)
        img = img.convert('RGB')
        img_array = np.array(img)
        height, width = img_array.shape[:2]

        # 确保尺寸是偶数
        if height % 2 != 0:
            height -= 1
        if width % 2 != 0:
            width -= 1

        img_resized = cv2.resize(img_array, (width, height))

        # 获取音频时长
        duration = 3.0  # 默认3秒
        try:
            result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1', audio_path
            ], capture_output=True, text=True)

            if result.returncode == 0:
                duration = float(result.stdout.strip())
        except:
            pass

        tracker.update(70, "生成视频帧...")

        # 创建临时视频
        temp_video = str(output_path.with_suffix('.temp.mp4'))
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        fps = 25
        total_frames = int(fps * duration)

        out = cv2.VideoWriter(temp_video, fourcc, fps, (width, height))

        # 生成简单的说话效果
        for frame_idx in range(total_frames):
            scale_factor = 1.0 + 0.01 * np.sin(frame_idx * 0.3)
            scaled_height = int(height * scale_factor)
            scaled_width = int(width * scale_factor)

            if scaled_height % 2 != 0:
                scaled_height -= 1
            if scaled_width % 2 != 0:
                scaled_width -= 1

            scaled_img = cv2.resize(img_resized, (scaled_width, scaled_height))

            if scaled_height > height or scaled_width > width:
                y_start = (scaled_height - height) // 2
                x_start = (scaled_width - width) // 2
                frame = scaled_img[y_start:y_start+height, x_start:x_start+width]
            else:
                frame = np.zeros((height, width, 3), dtype=np.uint8)
                y_start = (height - scaled_height) // 2
                x_start = (width - scaled_width) // 2
                frame[y_start:y_start+scaled_height, x_start:x_start+scaled_width] = scaled_img

            frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            out.write(frame)

        out.release()

        tracker.update(90, "合并音频...")

        # 合并音频
        cmd = [
            'ffmpeg', '-y',
            '-i', temp_video,
            '-i', audio_path,
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-shortest',
            str(output_path)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        # 清理临时文件
        if Path(temp_video).exists():
            Path(temp_video).unlink()

        if result.returncode == 0 and output_path.exists():
            logger.info("✅ 回退视频生成成功")
            return True
        else:
            logger.error(f"❌ 回退视频生成失败: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"❌ 回退视频生成异常: {e}")
        return False
