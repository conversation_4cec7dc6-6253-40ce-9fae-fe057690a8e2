import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { resolve } from 'path'
import { networkInterfaces } from 'os'
import { fileURLToPath, URL } from 'node:url'
import 'isomorphic-fetch'

// 获取本地 IP 地址的函数
function getLocalIP() {
  const nets = networkInterfaces();
  const results = {};

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // 跳过非 IPv4 和内部 IP
      if (net.family === 'IPv4' && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.address);
      }
    }
  }

  // 尝试找到第一个有效的 IP
  for (const name in results) {
    if (results[name].length > 0) {
      return results[name][0];
    }
  }

  // 如果找不到，使用 localhost
  return 'localhost';
}

// 获取本地 IP
const localIP = getLocalIP();
console.log(`检测到本地 IP: ${localIP}`);

// 定义可能的后端API服务器地址，按优先级排序
const possibleApiHosts = [
  // 本地回环地址（总是可用）
  'localhost',
  // 当前机器的实际IP地址（动态检测）
  localIP,
  // 固定的内网IP地址（如果有）
  '*************'
];

console.log(`配置的API服务器候选地址: ${possibleApiHosts.join(', ')}`);

// https://vitejs.dev/config/
export default defineConfig({
  base: '/',
  plugins: [
    vue(),
    vueJsx(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      'vue': 'vue/dist/vue.esm-bundler.js',
      // 添加组件别名，确保可以通过多种路径导入
      'PageHeader': fileURLToPath(new URL('./src/components/PageHeader.vue', import.meta.url)),
      '@components': fileURLToPath(new URL('./src/components', import.meta.url)),
      '@views': fileURLToPath(new URL('./src/views', import.meta.url))
    },
    // 添加.vue扩展名优先解析
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
  },
  optimizeDeps: {
    // 排除vue-router以避免可能的循环依赖
    exclude: ['vue-router'],
    // 包含需要预构建的依赖
    include: ['vue', 'element-plus', 'axios'],
    // 确保.vue文件也被正确预构建
    entries: [
      './src/main.js',
      './src/components/PageHeader.vue',
      './src/views/resume/ResumeGenerator.vue'
    ]
  },
  esbuild: {
    jsxFactory: 'h',
    jsxFragment: 'Fragment',
    jsxInject: `import { h } from 'vue'`,
    target: 'es2020',
    loader: {
      '.js': 'jsx',
      '.vue': 'jsx'
    }
  },
  define: {
    'process.env': {},
    'import.meta.env.VITE_APP_BASE_URL': JSON.stringify('/'),
    'import.meta.env.VITE_API_BASE_URL': JSON.stringify('/api'),
    // 添加可用API主机列表供前端代码使用
    'import.meta.env.VITE_POSSIBLE_API_HOSTS': JSON.stringify(possibleApiHosts)
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    historyApiFallback: true,
    // 添加MIME类型修复中间件
    configureServer: (server) => {
      server.middlewares.use((req, res, next) => {
        // 检查是否是.vue文件请求
        if (req.url && req.url.endsWith('.vue')) {
          console.log(`[vite:middleware] 处理.vue文件请求: ${req.url}`);
          // 设置正确的MIME类型
          res.setHeader('Content-Type', 'application/javascript');
        }
        
        // 处理常见的MIME类型问题文件
        if (req.url && (
            req.url.includes('formatter') ||
            req.url.includes('auth.js') ||
            req.url.includes('format-utils')
        )) {
          console.log(`[vite:middleware] 处理MIME类型问题文件: ${req.url}`);
          res.setHeader('Content-Type', 'application/javascript');
        }
        
        next();
      });
    },
    proxy: {
      // 统一处理所有API请求
      '/api': {
        // 使用环境变量或默认值，注意您的后端是8000端口
        target: process.env.VITE_API_BASE_URL || 'http://*************:8000',
        changeOrigin: true,
        secure: false,
        // 禁用重定向跟随，确保使用透明代理
        followRedirects: false,
        // 允许WebSocket代理
        ws: true,
        // 添加代理请求处理器，可以动态修改请求目标
        // 禁用307临时重定向，使用透明代理直接转发请求
        configure: (proxy, options) => {
          console.log(`[vite:proxy] 初始化代理配置，默认目标: http://${localIP}:8000`);
          
          // 记录当前工作的API主机
          let workingApiHost = null;
          
          // 添加请求错误处理器
          proxy.on('error', (err, req, res) => {
            const currentTarget = req.originalUrl || req.url;
            console.error(`[vite:proxy] 请求错误: ${err.message}, 目标: ${currentTarget}`);
            
            // 如果是连接失败，尝试下一个可能的主机
            if (err.code === 'ECONNREFUSED' && !req._retried) {
              req._retried = true;
              
              // 确定当前尝试的主机
              const currentHostIndex = possibleApiHosts.findIndex(host => 
                options.target.includes(host));
              
              // 尝试下一个主机
              if (currentHostIndex >= 0 && currentHostIndex < possibleApiHosts.length - 1) {
                const nextHost = possibleApiHosts[currentHostIndex + 1];
                const newTarget = `http://${nextHost}:8000`;
                
                console.log(`[vite:proxy] 尝试下一个API主机: ${nextHost}`);
                
                // 创建新的代理请求到下一个主机
                const newOptions = { ...options, target: newTarget };
                proxy.web(req, res, newOptions);
                return;
              }
            }
            
            // 如果所有主机都失败，返回错误响应
            if (!res.headersSent) {
              res.writeHead(500, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ 
                error: 'API服务器连接失败',
                message: '所有可能的API服务器地址均无法连接，请检查后端服务是否正在运行。',
                hosts_tried: possibleApiHosts 
              }));
            }
          });
          
          // 添加代理请求完成处理器
          proxy.on('proxyRes', (proxyRes, req, res) => {
            // 记录成功的主机
            if (!workingApiHost && req.originalHost) {
              workingApiHost = req.originalHost;
              console.log(`[vite:proxy] 找到可用的API主机: ${workingApiHost}`);
            }
          });
          
          // 修改代理请求
          proxy.on('proxyReq', (proxyReq, req, res, options) => {
            // 在请求中记录原始主机
            try {
              const targetUrl = typeof options.target === 'string' ? options.target : options.target.href || options.target.toString();
              req.originalHost = new URL(targetUrl).hostname;
            } catch (e) {
              console.warn(`[vite:proxy] 无法解析目标URL: ${options.target}`, e.message);
              req.originalHost = 'localhost';
            }
            
            console.log(`[vite:proxy] 代理请求: ${req.url} -> ${options.target}${req.url}`);
            
            // 添加自定义请求头，帮助后端识别请求来源
            proxyReq.setHeader('X-Forwarded-Host', req.headers.host || 'unknown');
            proxyReq.setHeader('X-Client-IP', req.socket.remoteAddress || 'unknown');
          });
          
          // 监听WebSocket升级事件
          proxy.on('upgrade', (req, socket, head) => {
            console.log('[vite:proxy] 处理WebSocket升级请求');
            
            // 确保请求携带认证信息
            if (req.headers.authorization) {
              console.log('[vite:proxy] WebSocket请求包含授权头');
            } else {
              // 尝试从URL参数中提取token
              try {
                const url = req.url;
                if (url.includes('token=')) {
                  const tokenMatch = url.match(/[?&]token=([^&]+)/);
                  if (tokenMatch && tokenMatch[1]) {
                    req.headers.authorization = decodeURIComponent(tokenMatch[1]);
                    console.log('[vite:proxy] 从URL参数提取token作为授权头');
                  }
                }
              } catch (e) {
                console.warn(`[vite:proxy] 处理WebSocket授权失败: ${e.message}`);
              }
            }
          });
        },
        // 确保认证头正确传递
        headers: {
          Connection: 'keep-alive'
        },

        // 禁用307临时重定向，使用透明代理直接转发请求
        bypass: (req, res, options) => {
          console.log(`[vite:proxy] 请求: ${req.url}`);
          // 不执行重定向，返回null使请求继续由代理处理
          return null;
        },
        // 自定义body解析器，以便捕获并保存原始请求体
        onReq: (req, options) => {
          // 记录WebSocket请求
          if (req.headers && req.headers.upgrade && req.headers.upgrade.toLowerCase() === 'websocket') {
            console.log(`[vite:proxy] 检测到WebSocket连接请求: ${req.url}`);
            
            // 如果WebSocket URL中包含token参数，提取并作为授权头添加
            if (req.url && req.url.includes('token=')) {
              try {
                const urlObj = new URL(req.url, `http://${req.headers.host || 'localhost'}`);
                const token = urlObj.searchParams.get('token');
                if (token) {
                  // 设置授权头
                  req.headers.authorization = token;
                  console.log('[vite:proxy] 从WebSocket URL参数提取token到授权头');
                }
              } catch (e) {
                console.warn(`[vite:proxy] 解析WebSocket URL参数失败: ${e.message}`);
              }
            }
          }
          
          // 特殊处理术语API请求
          if (req.url && req.url.includes('/terminology')) {
            console.log(`[vite:proxy] 检测到术语API请求: ${req.url}`);
            
            // 确保请求包含认证头
            if (req.headers && req.headers.authorization) {
              console.log('[vite:proxy] 术语API请求包含认证头');
              
              // 检查URL中是否包含auth_token参数
              if (!req.url.includes('auth_token=')) {
                // 从认证头中提取token
                const authHeader = req.headers.authorization;
                const token = authHeader.replace(/^bearer\s+/i, '');
                
                // 添加auth_token参数到URL
                const separator = req.url.includes('?') ? '&' : '?';
                req.url = `${req.url}${separator}auth_token=${token}`;
                console.log(`[vite:proxy] 添加auth_token参数到术语API请求URL: ${req.url.replace(/auth_token=[^&]+/, 'auth_token=***')}`);
              }
            } else {
              console.warn('[vite:proxy] 术语API请求没有认证头，可能会被拒绝');
            }
          }
          
          if (req.method !== 'GET' && req.method !== 'HEAD') {
            let rawBody = [];
            req.on('data', (chunk) => {
              rawBody.push(chunk);
            });
            req.on('end', () => {
              rawBody = Buffer.concat(rawBody);
              req.rawBody = rawBody;
              console.log(`[vite:proxy] 捕获到原始请求体，长度: ${rawBody.length}`);
            });
          }
          
          // 检查URL参数中是否有auth_token，如果没有且有Authorization头，则添加
          if (req.url && req.headers.authorization) {
            const authHeader = req.headers.authorization;
            // 提取Bearer令牌
            const token = authHeader.replace('Bearer ', '');
            
            // 如果URL中不包含auth_token参数，则添加
            if (!req.url.includes('auth_token=')) {
              const separator = req.url.includes('?') ? '&' : '?';
              req.url = `${req.url}${separator}auth_token=${token}`;
              console.log(`[vite:proxy] 添加auth_token到URL: ${req.url}`);
            }
          }
        }
      },
      // 添加静态资源代理规则
      '/static': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        // 增加此选项，防止代理自动跟随重定向
        followRedirects: false,
      },
      // 直接转发用户请求路径
      '/user': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        // 不需要重写路径，直接转发
        configure: (proxy, _options) => {
          console.log('[vite:proxy] 配置/user路径代理');
          
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // 调试日志
            console.log(`[vite:proxy] 处理/user请求: ${req.url}`);
            
            // 特别处理认证头
            if (req.headers.authorization) {
              // 确保authorization头格式正确（Bearer + token）
              let authHeader = req.headers.authorization;
              if (!authHeader.toLowerCase().startsWith('bearer ') && !authHeader.includes('emergency_test_token')) {
                authHeader = `Bearer ${authHeader}`;
              }
              
              // 设置认证头
              proxyReq.setHeader('Authorization', authHeader);
              
              console.log('[vite:proxy] 转发用户请求认证头:', authHeader.substring(0, 20) + '...');
            }
          });
        }
      },
      // 代理静态文件存储路径
      '/storage': {
        target: 'http://*************:8000',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          console.log('[vite:proxy] 配置/storage路径代理');

          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log(`[vite:proxy] 处理/storage请求: ${req.url}`);
          });
        }
      },
      // 直接转发健康检查请求到根路径
      '/health': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      // 处理API代理请求
      '/api-proxy': {
        target: 'http://*************:8000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          console.log(`[vite:proxy] 处理API代理请求: ${path}`);
          // 正确处理路径，去除api-proxy前缀，保留其他部分
          const newPath = path.replace(/^\/api-proxy/, '');
          console.log(`[vite:proxy] 路径重写: ${path} -> ${newPath}`);
          return newPath;
        }
      },
      // 处理直接的用户路径 /users/me 请求
      '/users': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      // 添加对TTS路径的直接代理
      '/tts': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          console.log('[vite:proxy] 配置/tts路径代理');
          
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // 调试日志
            console.log(`[vite:proxy] 处理/tts请求: ${req.url}`);
            
            // 特别处理认证头
            if (req.headers.authorization) {
              // 确保authorization头格式正确（Bearer + token）
              let authHeader = req.headers.authorization;
              if (!authHeader.toLowerCase().startsWith('bearer ') && !authHeader.includes('emergency_test_token')) {
                authHeader = `Bearer ${authHeader}`;
              }
              
              // 设置认证头
              proxyReq.setHeader('Authorization', authHeader);
              
              console.log('[vite:proxy] 转发TTS请求认证头:', authHeader.substring(0, 20) + '...');
            }
          });
        }
      },
      // 添加专门用于处理重定向的路径
      '/_redirect': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('[vite:proxy] 处理重定向请求:', req.url);
            
            // 从查询参数中获取目标URL和授权头
            const url = new URL(req.url, 'http://localhost:3000');
            const target = url.searchParams.get('target');
            const auth = url.searchParams.get('auth');
            
            if (!target) {
              res.statusCode = 400;
              res.end(JSON.stringify({ error: '缺少target参数' }));
              return;
            }
            
            // 设置授权头
            if (auth) {
              proxyReq.setHeader('Authorization', auth);
            }
            
            // 修改请求URL为目标URL
            try {
              const targetUrl = new URL(target);
              req.url = targetUrl.pathname + targetUrl.search;
            } catch (e) {
              console.error(`[vite:proxy] 无法解析目标URL: ${target}`, e.message);
              res.writeHead(400, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ error: '无效的目标URL' }));
              return;
            }
            
            console.log(`[vite:proxy] 重定向请求URL修改为: ${req.url}`);
          });
        }
      }
    },
    watch: {
      usePolling: true
    },
    // 添加自定义中间件处理错误
    middlewares: [
      (req, res, next) => {
        // 检查是否是对Vue文件的直接请求
        const isVueFileRequest = req.url && (
          req.url.endsWith('.vue') || 
          req.url.includes('.vue?') || 
          req.url.includes('/src/views/')
        );
        
        if (isVueFileRequest) {
          console.warn(`[vite:server] 检测到对Vue源文件的直接请求: ${req.url}`);
          
          // 返回友好的错误响应，而不是500错误
          res.writeHead(400, {
            'Content-Type': 'application/json',
          });
          
          res.end(JSON.stringify({
            code: 'DIRECT_VUE_FILE_ACCESS',
            message: '无法直接访问Vue源文件，请通过正确的路由访问编译后的资源',
            url: req.url,
            solution: '请检查您的路由配置和组件导入路径',
            timestamp: new Date().toISOString()
          }));
          
          return; // 不继续处理请求
        }
        
        // 为所有响应添加错误处理逻辑
        const originalEnd = res.end;
        res.end = function() {
          // 检查是否是5xx错误响应
          if (res.statusCode >= 500) {
            console.error(`[vite:server] 服务器错误 ${res.statusCode}: ${req.url}`);
            
            // 记录错误详情以便调试
            console.error(`[vite:server] 请求方法: ${req.method}`);
            console.error(`[vite:server] 请求头: ${JSON.stringify(req.headers)}`);
            
            // 尝试打印请求体
            if (req.rawBody) {
              try {
                const bodyStr = req.rawBody.toString('utf8');
                console.error(`[vite:server] 请求体: ${bodyStr.substring(0, 200)}${bodyStr.length > 200 ? '...' : ''}`);
              } catch (e) {
                console.error(`[vite:server] 无法读取请求体: ${e.message}`);
              }
            }
          }
          
          // 调用原始的end方法
          return originalEnd.apply(this, arguments);
        };
        
        next();
      }
    ]
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    minify: 'esbuild',
    terserOptions: {
      compress: {
        drop_console: false,
        drop_debugger: false
      },
      keep_classnames: true,
      keep_fnames: true
    },
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            return 'vendor';
          }
          if (id.includes('/src/components/')) {
            return 'components';
          }
          if (id.includes('/src/views/')) {
            return 'views';
          }
        }
      }
    },
    target: 'es2015',
    commonjsOptions: {
      transformMixedEsModules: true
    },
    assetsInlineLimit: 4096,
    cssCodeSplit: true,
    sourcemap: true
  }
}) 