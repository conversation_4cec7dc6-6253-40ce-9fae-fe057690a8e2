<template>
  <div class="complete-step">
    <div class="step-header">
      <h2 class="step-title">完成创建</h2>
      <p class="step-description">确认信息并生成您的专属数字人</p>
    </div>

    <div class="complete-content">
      <!-- 信息确认 -->
      <div class="info-summary">
        <h4>📋 信息确认</h4>
        <div class="summary-grid">
          <div class="summary-item">
            <label>数字人名称</label>
            <span>{{ formData.name || '未设置' }}</span>
          </div>
          
          <div class="summary-item">
            <label>数字人类型</label>
            <span>{{ getTypeText(formData.type) }}</span>
          </div>
          
          <div class="summary-item">
            <label>性别</label>
            <span>{{ getGenderText(formData.gender) }}</span>
          </div>
          
          <div class="summary-item">
            <label>外观设置</label>
            <span>{{ getAppearanceText() }}</span>
          </div>
          
          <div class="summary-item">
            <label>声音设置</label>
            <span>{{ getVoiceText() }}</span>
          </div>
          
          <div class="summary-item full-width">
            <label>个性描述</label>
            <span>{{ formData.description || '未设置' }}</span>
          </div>
          
          <div class="summary-item full-width">
            <label>欢迎语</label>
            <span>"{{ formData.welcomeText || '未设置' }}"</span>
          </div>
        </div>
      </div>

      <!-- 生成选项 -->
      <div class="generation-options">
        <h4>⚙️ 生成选项</h4>
        <div class="options-grid">
          <div class="option-item">
            <label>生成质量</label>
            <el-select v-model="generationOptions.quality" placeholder="选择质量">
              <el-option label="标准质量" value="standard" />
              <el-option label="高质量" value="high" />
              <el-option label="超高质量" value="ultra" />
            </el-select>
          </div>
          
          <div class="option-item">
            <label>输出格式</label>
            <el-select v-model="generationOptions.format" placeholder="选择格式">
              <el-option label="MP4视频" value="mp4" />
              <el-option label="WebM视频" value="webm" />
              <el-option label="GIF动图" value="gif" />
            </el-select>
          </div>
          
          <div class="option-item">
            <label>分辨率</label>
            <el-select v-model="generationOptions.resolution" placeholder="选择分辨率">
              <el-option label="720P" value="720p" />
              <el-option label="1080P" value="1080p" />
              <el-option label="4K" value="4k" />
            </el-select>
          </div>
          
          <div class="option-item">
            <label>帧率</label>
            <el-select v-model="generationOptions.fps" placeholder="选择帧率">
              <el-option label="24 FPS" value="24" />
              <el-option label="30 FPS" value="30" />
              <el-option label="60 FPS" value="60" />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 高级设置 -->
      <div class="advanced-options">
        <h4>🔧 高级设置</h4>
        <div class="advanced-grid">
          <div class="advanced-item">
            <el-checkbox v-model="generationOptions.enableLipSync">
              启用唇形同步
            </el-checkbox>
            <span class="option-desc">让数字人的嘴部动作与语音同步</span>
          </div>
          
          <div class="advanced-item">
            <el-checkbox v-model="generationOptions.enableGestures">
              启用手势动作
            </el-checkbox>
            <span class="option-desc">添加自然的手势和肢体动作</span>
          </div>
          
          <div class="advanced-item">
            <el-checkbox v-model="generationOptions.enableEyeContact">
              启用眼神交流
            </el-checkbox>
            <span class="option-desc">让数字人的眼神更加自然生动</span>
          </div>
          
          <div class="advanced-item">
            <el-checkbox v-model="generationOptions.enableBackground">
              启用背景环境
            </el-checkbox>
            <span class="option-desc">添加适合的背景环境</span>
          </div>
        </div>
      </div>

      <!-- 创建提示 -->
      <div class="creation-tips">
        <h4>💡 创建提示</h4>
        <div class="tips-content">
          <div class="tip-item">
            <span class="tip-icon">⚡</span>
            <span class="tip-text">生成过程大约需要1-2分钟</span>
          </div>
          <div class="tip-item">
            <span class="tip-icon">🎯</span>
            <span class="tip-text">生成完成后可立即开始对话</span>
          </div>
          <div class="tip-item">
            <span class="tip-icon">💾</span>
            <span class="tip-text">数字人将自动保存到您的账户</span>
          </div>
        </div>
      </div>

      <!-- 创建按钮 -->
      <div class="creation-actions">
        <el-button 
          size="large" 
          @click="saveAsDraft"
          :loading="isSavingDraft"
        >
          <el-icon><Document /></el-icon>
          保存为草稿
        </el-button>
        
        <el-button 
          type="primary" 
          size="large" 
          @click="startCreation"
          :loading="isCreating"
          :disabled="!canCreate"
        >
          <el-icon><Star /></el-icon>
          开始生成数字人
        </el-button>
      </div>

      <!-- 调试状态信息 -->
      <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; font-size: 12px;">
        <div>isCreating: {{ isCreating }}</div>
        <div>generationResult: {{ !!generationResult }}</div>
        <div>generationProgress: {{ generationProgress }}</div>
        <div>currentProgressStep: {{ currentProgressStep }}</div>
        <div v-if="generationResult">digitalHumanId: {{ generationResult.digitalHumanId }}</div>
        <div v-if="generationResult">resultUrls: {{ Object.keys(generationResult.resultUrls || {}) }}</div>
      </div>

      <!-- 生成进度 -->
      <div v-if="(isCreating || generationProgress > 0) && !generationResult" class="generation-progress">
        <h4>🚀 正在生成数字人...</h4>
        <el-progress
          :percentage="generationProgress"
          :stroke-width="8"
          :color="getProgressColor(generationProgress)"
        >
          <template #default="{ percentage }">
            <span class="progress-text">{{ percentage }}%</span>
          </template>
        </el-progress>

        <div class="progress-steps">
          <div
            v-for="(step, index) in progressSteps"
            :key="index"
            class="progress-step"
            :class="{
              'active': currentProgressStep === index,
              'completed': currentProgressStep > index
            }"
          >
            <div class="step-icon">
              <el-icon v-if="currentProgressStep > index"><Check /></el-icon>
              <el-icon v-else-if="currentProgressStep === index" class="spinning"><Loading /></el-icon>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <span class="step-text">{{ step }}</span>
          </div>
        </div>
      </div>

      <!-- 生成结果展示 -->
      <div v-if="generationResult" class="generation-result">
        <div class="result-header">
          <div class="success-animation">
            <div class="success-icon">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <h3 class="success-title">🎉 数字人生成成功！</h3>
            <p class="success-subtitle">您的专属数字人已经准备就绪，可以开始对话了</p>
          </div>

          <!-- 快速操作按钮 -->
          <div class="quick-actions">
            <el-button
              type="primary"
              size="large"
              @click="startChat"
              class="primary-action"
            >
              <el-icon><ChatDotRound /></el-icon>
              立即开始对话
            </el-button>
            <el-button
              size="large"
              @click="downloadAllFiles"
              class="secondary-action"
            >
              <el-icon><Download /></el-icon>
              下载所有文件
            </el-button>
          </div>
        </div>

        <div class="result-content">
          <!-- 数字人预览 -->
          <div class="digital-human-preview">
            <div class="preview-container">
              <!-- 头像预览 -->
              <div class="avatar-preview">
                <h4>📸 数字人头像</h4>
                <div class="avatar-container">
                  <img
                    v-if="generationResult.resultUrls?.avatar_url"
                    :src="generationResult.resultUrls.avatar_url"
                    alt="数字人头像"
                    class="avatar-image"
                    @error="handleImageError"
                  />
                  <div v-else class="avatar-placeholder">
                    <el-icon><Picture /></el-icon>
                    <span>头像生成中...</span>
                  </div>
                </div>
                <div class="avatar-actions">
                  <el-button
                    size="small"
                    @click="downloadAvatar"
                    :disabled="!generationResult.resultUrls?.avatar_url"
                  >
                    <el-icon><Download /></el-icon>
                    下载头像
                  </el-button>
                </div>
              </div>

              <!-- 视频预览 -->
              <div class="video-preview">
                <div class="preview-header">
                  <h4>🎬 数字人说话视频</h4>
                  <div class="video-info">
                    <span class="video-duration">{{ getVideoDuration() }}</span>
                    <span class="video-quality">高清画质</span>
                  </div>
                </div>

                <div class="video-container">
                  <video
                    v-if="getVideoUrl()"
                    :src="getVideoUrl()"
                    controls
                    preload="metadata"
                    class="preview-video"
                    @error="handleVideoError"
                    @loadedmetadata="onVideoLoaded"
                    poster=""
                  >
                    您的浏览器不支持视频播放
                  </video>
                  <div v-else class="video-placeholder">
                    <div class="placeholder-content">
                      <el-icon class="placeholder-icon"><VideoPlay /></el-icon>
                      <span class="placeholder-text">视频生成中...</span>
                      <div class="placeholder-progress">
                        <el-progress :percentage="generationProgress" :show-text="false" />
                      </div>
                    </div>
                  </div>

                  <!-- 视频控制覆盖层 -->
                  <div v-if="getVideoUrl()" class="video-overlay">
                    <div class="video-controls">
                      <el-button
                        circle
                        size="large"
                        @click="toggleVideoPlay"
                        class="play-button"
                      >
                        <el-icon><VideoPlay v-if="!isVideoPlaying" /><VideoPause v-else /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>

                <div class="video-actions">
                  <el-button
                    @click="downloadVideo"
                    :disabled="!getVideoUrl()"
                    :loading="isDownloadingVideo"
                  >
                    <el-icon><Download /></el-icon>
                    下载视频
                  </el-button>
                  <el-button
                    @click="shareVideo"
                    :disabled="!getVideoUrl()"
                  >
                    <el-icon><Share /></el-icon>
                    分享视频
                  </el-button>
                  <el-button
                    type="primary"
                    @click="previewInFullscreen"
                    :disabled="!getVideoUrl()"
                  >
                    <el-icon><FullScreen /></el-icon>
                    全屏预览
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 数字人信息 -->
          <div class="digital-human-info">
            <h4>ℹ️ 数字人信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>数字人ID</label>
                <span>{{ generationResult.digitalHumanId }}</span>
              </div>
              <div class="info-item">
                <label>生成时间</label>
                <span>{{ formatDate(generationResult.createdAt) }}</span>
              </div>
              <div class="info-item">
                <label>文件大小</label>
                <span>{{ getFileSize() }}</span>
              </div>
              <div class="info-item">
                <label>视频时长</label>
                <span>{{ getVideoDuration() }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="result-actions">
            <el-button size="large" @click="createAnother">
              <el-icon><Plus /></el-icon>
              创建新的数字人
            </el-button>
            <el-button size="large" type="primary" @click="goToManagement">
              <el-icon><Setting /></el-icon>
              管理我的数字人
            </el-button>
            <el-button size="large" @click="shareDigitalHuman">
              <el-icon><Share /></el-icon>
              分享数字人
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Document,
  Star,
  Check,
  Loading,
  Picture,
  VideoPlay,
  VideoPause,
  Download,
  ChatDotRound,
  Plus,
  Setting,
  Share,
  SuccessFilled,
  FullScreen
} from '@element-plus/icons-vue';
import { DIGITAL_HUMAN_TYPES, GENDER_OPTIONS } from '../utils/constants.js';
import { DigitalHumanGenerationManager } from '../api/digitalHumanGeneration.js';

export default defineComponent({
  name: 'CompleteStep',
  
  components: {
    Document,
    Star,
    Check,
    Loading,
    Picture,
    VideoPlay,
    Download,
    ChatDotRound,
    Plus,
    Setting,
    Share
  },
  
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  
  emits: ['create'],
  
  setup(props, { emit }) {
    const isSavingDraft = ref(false);
    const isCreating = ref(false);
    const generationProgress = ref(0);
    const currentProgressStep = ref(0);
    const generationResult = ref(null);

    // 数字人生成管理器
    const digitalHumanGenerationManager = new DigitalHumanGenerationManager();

    // 视频相关状态
    const isVideoPlaying = ref(false);
    const isDownloadingVideo = ref(false);
    const videoElement = ref(null);

    // 生成选项
    const generationOptions = ref({
      quality: 'high',
      format: 'mp4',
      resolution: '1080p',
      fps: '30',
      enableLipSync: true,
      enableGestures: true,
      enableEyeContact: true,
      enableBackground: false
    });
    
    // 进度步骤
    const progressSteps = [
      '准备生成环境',
      '处理外观数据',
      '合成声音模型',
      '渲染数字人形象',
      '优化输出质量',
      '完成生成'
    ];
    
    // 是否可以创建
    const canCreate = computed(() => {
      return !!(props.formData.name && 
                props.formData.selectedTemplate && 
                props.formData.selectedVoice);
    });
    
    // 获取类型文本
    const getTypeText = (type) => {
      const typeItem = DIGITAL_HUMAN_TYPES.find(t => t.value === type);
      return typeItem?.label || type;
    };
    
    // 获取性别文本
    const getGenderText = (gender) => {
      const genderItem = GENDER_OPTIONS.find(g => g.value === gender);
      return genderItem?.label || gender;
    };
    
    // 获取外观文本
    const getAppearanceText = () => {
      if (props.formData.appearanceType === 'upload') {
        return '自定义上传';
      }
      return '预设模板';
    };
    
    // 获取声音文本
    const getVoiceText = () => {
      return '预设声音';
    };
    

    
    // 获取进度条颜色
    const getProgressColor = (percentage) => {
      if (percentage < 30) return '#f59e0b';
      if (percentage < 70) return '#3b82f6';
      return '#10b981';
    };
    
    // 保存草稿
    const saveAsDraft = async () => {
      isSavingDraft.value = true;
      
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        ElMessage.success('草稿保存成功');
      } catch (error) {
        ElMessage.error('保存失败，请重试');
      } finally {
        isSavingDraft.value = false;
      }
    };
    
    // 开始创建
    const startCreation = async () => {
      if (!canCreate.value) {
        ElMessage.warning('请完善必填信息');
        return;
      }
      
      try {
        await ElMessageBox.confirm(
          '确认开始生成数字人？此过程可能需要几分钟时间。',
          '确认生成',
          {
            confirmButtonText: '开始生成',
            cancelButtonText: '取消',
            type: 'info'
          }
        );

        // 直接触发父组件的创建方法，不在这里处理进度
        emit('create', props.formData);

      } catch (error) {
        if (error !== 'cancel') {
          console.log('用户取消创建');
        }
      }
    };
    
    // 结果处理方法
    const handleImageError = (event) => {
      console.error('头像加载失败:', event);
      ElMessage.error('头像加载失败');
    };

    const handleVideoError = (event) => {
      console.error('视频加载失败:', event);
      ElMessage.error('视频加载失败');
    };

    const downloadAvatar = () => {
      if (generationResult.value?.resultUrls?.avatar_url) {
        const link = document.createElement('a');
        link.href = generationResult.value.resultUrls.avatar_url;
        link.download = `digital_human_avatar_${generationResult.value.digitalHumanId}.jpg`;
        link.click();
      }
    };

    const downloadVideo = () => {
      if (generationResult.value?.resultUrls?.preview_url) {
        const link = document.createElement('a');
        link.href = generationResult.value.resultUrls.preview_url;
        link.download = `digital_human_video_${generationResult.value.digitalHumanId}.mp4`;
        link.click();
      }
    };

    const startChat = () => {
      // 跳转到数字人聊天页面
      const chatUrl = `${window.location.origin}/digital-human/chat/${generationResult.value.digitalHumanId}`;
      window.open(chatUrl, '_blank');
    };

    const createAnother = () => {
      // 重置状态，创建新的数字人
      generationResult.value = null;
      generationProgress.value = 0;
      currentProgressStep.value = 0;
      isCreating.value = false;
    };

    const goToManagement = () => {
      // 跳转到数字人应用中心
      const appUrl = `${window.location.origin}/digital-human/app`;
      window.open(appUrl, '_blank');
    };

    const shareDigitalHuman = () => {
      // 分享数字人
      const shareUrl = `${window.location.origin}/digital-human/view/${generationResult.value.digitalHumanId}`;
      navigator.clipboard.writeText(shareUrl).then(() => {
        ElMessage.success('分享链接已复制到剪贴板');
      }).catch(() => {
        ElMessage.error('复制失败，请手动复制链接');
      });
    };

    const formatDate = (date) => {
      if (!date) return '';
      return new Date(date).toLocaleString('zh-CN');
    };

    const getFileSize = () => {
      // 模拟文件大小计算
      return '约 2.1MB';
    };

    const getVideoDuration = () => {
      // 模拟视频时长
      return '约 8秒';
    };

    // 视频相关方法
    const getVideoUrl = () => {
      return generationResult.value?.resultUrls?.preview_video_url ||
             generationResult.value?.resultUrls?.preview_url;
    };

    const toggleVideoPlay = () => {
      const video = document.querySelector('.preview-video');
      if (video) {
        if (video.paused) {
          video.play();
          isVideoPlaying.value = true;
        } else {
          video.pause();
          isVideoPlaying.value = false;
        }
      }
    };

    const onVideoLoaded = (event) => {
      const video = event.target;
      console.log('视频加载完成:', video.duration);
    };

    const downloadVideo = async () => {
      const videoUrl = getVideoUrl();
      if (!videoUrl) {
        ElMessage.warning('视频还未生成完成');
        return;
      }

      try {
        isDownloadingVideo.value = true;

        // 创建下载链接
        const link = document.createElement('a');
        link.href = videoUrl;
        link.download = `数字人预览视频_${generationResult.value?.digitalHumanId || 'unknown'}.mp4`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        ElMessage.success('视频下载已开始');
      } catch (error) {
        console.error('下载视频失败:', error);
        ElMessage.error('下载失败，请重试');
      } finally {
        isDownloadingVideo.value = false;
      }
    };

    const downloadAllFiles = async () => {
      try {
        const files = [];

        // 添加头像
        if (generationResult.value?.resultUrls?.avatar_url) {
          files.push({
            url: generationResult.value.resultUrls.avatar_url,
            name: `数字人头像_${generationResult.value.digitalHumanId}.jpg`
          });
        }

        // 添加视频
        if (getVideoUrl()) {
          files.push({
            url: getVideoUrl(),
            name: `数字人视频_${generationResult.value.digitalHumanId}.mp4`
          });
        }

        // 逐个下载文件
        for (const file of files) {
          const link = document.createElement('a');
          link.href = file.url;
          link.download = file.name;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // 添加延迟避免浏览器阻止多个下载
          await new Promise(resolve => setTimeout(resolve, 500));
        }

        ElMessage.success(`开始下载 ${files.length} 个文件`);
      } catch (error) {
        console.error('批量下载失败:', error);
        ElMessage.error('下载失败，请重试');
      }
    };

    const shareVideo = () => {
      const videoUrl = getVideoUrl();
      if (!videoUrl) {
        ElMessage.warning('视频还未生成完成');
        return;
      }

      // 构建完整的视频URL
      const fullVideoUrl = videoUrl.startsWith('http') ? videoUrl : `${window.location.origin}${videoUrl}`;

      if (navigator.share) {
        // 使用原生分享API
        navigator.share({
          title: '我的数字人视频',
          text: '看看我创建的数字人！',
          url: fullVideoUrl
        }).catch(console.error);
      } else {
        // 复制到剪贴板
        navigator.clipboard.writeText(fullVideoUrl).then(() => {
          ElMessage.success('视频链接已复制到剪贴板');
        }).catch(() => {
          ElMessage.error('复制失败');
        });
      }
    };

    const previewInFullscreen = () => {
      const videoUrl = getVideoUrl();
      if (!videoUrl) {
        ElMessage.warning('视频还未生成完成');
        return;
      }

      // 在新窗口中打开视频
      const newWindow = window.open('', '_blank', 'width=800,height=600');
      newWindow.document.write(`
        <html>
          <head>
            <title>数字人视频预览</title>
            <style>
              body { margin: 0; background: #000; display: flex; justify-content: center; align-items: center; height: 100vh; }
              video { max-width: 100%; max-height: 100%; }
            </style>
          </head>
          <body>
            <video controls autoplay>
              <source src="${videoUrl}" type="video/mp4">
              您的浏览器不支持视频播放
            </video>
          </body>
        </html>
      `);
    };

    return {
      isSavingDraft,
      isCreating,
      generationProgress,
      currentProgressStep,
      generationResult,
      generationOptions,
      progressSteps,
      canCreate,
      getTypeText,
      getGenderText,
      getAppearanceText,
      getVoiceText,

      // 视频相关状态和方法
      isVideoPlaying,
      isDownloadingVideo,
      videoElement,
      getVideoUrl,
      toggleVideoPlay,
      onVideoLoaded,
      downloadAllFiles,
      shareVideo,
      previewInFullscreen,

      getProgressColor,
      saveAsDraft,
      startCreation,
      handleImageError,
      handleVideoError,
      downloadAvatar,
      downloadVideo,
      startChat,
      createAnother,
      goToManagement,
      shareDigitalHuman,
      formatDate,
      getFileSize,
      getVideoDuration
    };
  }
});
</script>

<style scoped>
.complete-step {
  padding: 32px;
  max-width: 800px;
}

.step-header {
  margin-bottom: 32px;
}

.step-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #f9fafb;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.step-description {
  margin: 0;
  font-size: 16px;
  color: #9ca3af;
  line-height: 1.5;
}

.complete-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* 信息确认样式 */
.info-summary h4,
.generation-options h4,
.advanced-options h4,
.estimation-info h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #f9fafb;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-item.full-width {
  grid-column: 1 / -1;
}

.summary-item label {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 500;
}

.summary-item span {
  font-size: 14px;
  color: #f9fafb;
  font-weight: 500;
}

/* 生成选项样式 */
.options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.option-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-item label {
  font-size: 14px;
  font-weight: 500;
  color: #d1d5db;
}

/* 高级设置样式 */
.advanced-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.advanced-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
}

.option-desc {
  font-size: 12px;
  color: #9ca3af;
  margin-left: 24px;
}

/* 创建提示样式 */
.creation-tips {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.creation-tips h4 {
  margin: 0 0 16px 0;
  color: #60a5fa;
  font-size: 16px;
  font-weight: 600;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tip-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.tip-text {
  color: #f9fafb;
  font-size: 14px;
  line-height: 1.4;
}

/* 创建按钮样式 */
.creation-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 生成进度样式 */
.generation-progress {
  padding: 24px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.generation-progress h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #3b82f6;
  text-align: center;
}

.progress-text {
  font-size: 14px;
  font-weight: 600;
  color: #f9fafb;
}

.progress-steps {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.progress-step.active {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.progress-step.completed {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(107, 114, 128, 0.3);
  color: #9ca3af;
  font-size: 12px;
  font-weight: 600;
}

.progress-step.active .step-icon {
  background: rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.progress-step.completed .step-icon {
  background: rgba(34, 197, 94, 0.3);
  color: #22c55e;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 生成结果样式 */
.generation-result {
  margin-top: 32px;
  padding: 40px;
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%);
  border-radius: 20px;
  border: 1px solid rgba(16, 185, 129, 0.2);
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.generation-result::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.result-header {
  text-align: center;
  margin-bottom: 40px;
}

.success-animation {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-icon {
  font-size: 48px;
  color: #10b981;
  margin-bottom: 16px;
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.success-title {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #10b981, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.success-subtitle {
  margin: 0 0 32px 0;
  font-size: 16px;
  color: #9ca3af;
  line-height: 1.5;
}

.quick-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.primary-action {
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.primary-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

.secondary-action {
  background: rgba(55, 65, 81, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #f9fafb;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.secondary-action:hover {
  background: rgba(75, 85, 99, 0.9);
  transform: translateY(-1px);
}

/* 视频预览样式 */
.video-preview {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.preview-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #f9fafb;
}

.video-info {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.video-duration {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
}

.video-quality {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
}

.video-container {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background: #000;
  margin-bottom: 20px;
}

.preview-video {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 12px;
}

.video-placeholder {
  aspect-ratio: 16/9;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1f2937, #111827);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 12px;
}

.placeholder-content {
  text-align: center;
  color: #9ca3af;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.6;
}

.placeholder-text {
  display: block;
  font-size: 16px;
  margin-bottom: 16px;
}

.placeholder-progress {
  width: 200px;
  margin: 0 auto;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-container:hover .video-overlay {
  opacity: 1;
}

.play-button {
  background: rgba(16, 185, 129, 0.9);
  border: none;
  color: white;
  font-size: 24px;
  width: 60px;
  height: 60px;
  backdrop-filter: blur(10px);
}

.play-button:hover {
  background: rgba(16, 185, 129, 1);
  transform: scale(1.1);
}

.video-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.video-actions .el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.video-actions .el-button:hover {
  transform: translateY(-1px);
}

.result-header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #0369a1;
}

.result-header p {
  margin: 0;
  font-size: 16px;
  color: #64748b;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.digital-human-preview {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.preview-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.avatar-preview,
.video-preview {
  text-align: center;
}

.avatar-preview h4,
.video-preview h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.avatar-container,
.video-container {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
}

.preview-video {
  width: 100%;
  height: 200px;
  border-radius: 8px;
}

.avatar-placeholder,
.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #9ca3af;
}

.avatar-placeholder .el-icon,
.video-placeholder .el-icon {
  font-size: 48px;
}

.avatar-actions,
.video-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.digital-human-info {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.digital-human-info h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.info-item span {
  font-size: 16px;
  color: #374151;
}

.result-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .preview-container {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .result-actions {
    flex-direction: column;
  }
}

.step-text {
  font-size: 14px;
  color: #d1d5db;
}

.progress-step.active .step-text {
  color: #3b82f6;
  font-weight: 500;
}

.progress-step.completed .step-text {
  color: #22c55e;
}
</style>
