// 数字人API服务

// 动态获取 API 基础 URL
const getApiBaseUrl = () => {
  const { hostname, protocol } = window.location;

  // 统一使用相对路径，让 Vite 代理处理
  // 这样可以避免CORS跨域问题
  return '';
};

// 获取资源文件的完整URL
const getResourceUrl = (relativePath) => {
  if (!relativePath) return null;

  // 如果已经是完整URL，直接返回
  if (relativePath.startsWith('http://') || relativePath.startsWith('https://')) {
    return relativePath;
  }

  const { hostname } = window.location;

  // 如果是相对路径，构建完整URL
  if (relativePath.startsWith('/storage/')) {
    // 本地开发环境，使用Vite代理
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return relativePath; // Vite会自动代理/storage路径
    } else {
      // 外网访问，使用前端服务器代理
      return relativePath; // 前端服务器已配置/storage代理
    }
  }

  return relativePath;
};

// 获取数字人列表
export async function getDigitalHumanList() {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/v1/digital-human/list`);
    return await response.json();
  } catch (error) {
    console.error('获取数字人列表失败:', error);
    return {
      success: false,
      error: error.message,
      digitalHumans: []
    };
  }
}

// 创建数字人
export async function createDigitalHuman(data) {
  try {
    const formData = new FormData();
    
    // 添加基本信息
    formData.append('name', data.name);
    formData.append('description', data.description);
    formData.append('voice_id', data.voiceId);
    formData.append('model_type', data.modelType);
    
    // 添加图片文件
    if (data.avatar) {
      formData.append('avatar', data.avatar);
    }
    
    // 添加视频文件
    if (data.video) {
      formData.append('video', data.video);
    }

    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/digital-human/create`, {
      method: 'POST',
      body: formData
    });
    
    return await response.json();
  } catch (error) {
    console.error('创建数字人失败:', error);
    return { success: false, error: error.message };
  }
}

// 更新数字人
export async function updateDigitalHuman(id, data) {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/digital-human/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    return await response.json();
  } catch (error) {
    console.error('更新数字人失败:', error);
    return { success: false, error: error.message };
  }
}

// 删除数字人
export async function deleteDigitalHuman(id) {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/digital-human/${id}`, {
      method: 'DELETE'
    });
    return await response.json();
  } catch (error) {
    console.error('删除数字人失败:', error);
    return { success: false, error: error.message };
  }
}

// 获取数字人详情
export async function getDigitalHumanDetail(id) {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/v1/digital-human/${id}`);
    return await response.json();
  } catch (error) {
    console.error('获取数字人详情失败:', error);
    return { success: false, error: error.message };
  }
}

// 数字人对话
export async function chatWithDigitalHuman(id, message) {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/digital-human/${id}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message })
    });
    return await response.json();
  } catch (error) {
    console.error('数字人对话失败:', error);
    return { success: false, error: error.message };
  }
}

// 生成数字人视频
export async function generateDigitalHumanVideo(id, text) {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/digital-human/${id}/generate-video`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text })
    });
    return await response.json();
  } catch (error) {
    console.error('生成数字人视频失败:', error);
    return { success: false, error: error.message };
  }
}

// 获取可用模型列表
export async function getAvailableModels() {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/digital-human/models`);
    return await response.json();
  } catch (error) {
    console.error('获取模型列表失败:', error);
    return { 
      success: false, 
      error: error.message,
      models: [
        { id: 'default', name: '默认模型', status: 'available' },
        { id: 'advanced', name: '高级模型', status: 'available' }
      ]
    };
  }
}

// 获取语音列表
export async function getVoiceList() {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/digital-human/voices`);
    return await response.json();
  } catch (error) {
    console.error('获取语音列表失败:', error);
    return { 
      success: false, 
      error: error.message,
      voices: [
        { id: 'female1', name: '女声1', language: 'zh-CN' },
        { id: 'male1', name: '男声1', language: 'zh-CN' },
        { id: 'female2', name: '女声2', language: 'en-US' },
        { id: 'male2', name: '男声2', language: 'en-US' }
      ]
    };
  }
}

// 获取唇同步数据
export async function getLipSyncData(id, text) {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/digital-human/${id}/lip-sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text })
    });
    return await response.json();
  } catch (error) {
    console.error('获取唇同步数据失败:', error);
    return { success: false, error: error.message };
  }
}

// 检查Ollama代理可用性
export async function checkOllamaProxyAvailable() {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/ollama/health`);
    return await response.json();
  } catch (error) {
    console.error('检查Ollama代理失败:', error);
    return { success: false, error: error.message };
  }
}

// 获取Ollama模型列表
export async function getOllamaModels() {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/ollama/models`);
    return await response.json();
  } catch (error) {
    console.error('获取Ollama模型失败:', error);
    return { success: false, error: error.message, models: [] };
  }
}

// 获取Ollama设置
export async function getOllamaSettings() {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/ollama/settings`);
    return await response.json();
  } catch (error) {
    console.warn('Ollama服务不可用，使用默认设置:', error.message);
    return {
      success: false,
      error: error.message,
      // 返回默认设置
      default: true,
      model: 'qwen2.5:7b',
      temperature: 0.7,
      max_tokens: 2000
    };
  }
}

// 保存Ollama设置
export async function saveOllamaSettings(settings) {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/ollama/settings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(settings)
    });
    return await response.json();
  } catch (error) {
    console.error('保存Ollama设置失败:', error);
    return { success: false, error: error.message };
  }
}

// 导出工具函数
export { getApiBaseUrl, getResourceUrl };
