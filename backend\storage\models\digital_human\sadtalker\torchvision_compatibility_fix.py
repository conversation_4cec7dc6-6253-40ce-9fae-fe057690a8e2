"""
Torchvision compatibility fix for <PERSON><PERSON>alker
This module provides compatibility patches for newer versions of torchvision
"""

import sys
import importlib.util
import warnings

def apply_torchvision_compatibility_fix():
    """Apply compatibility fixes for torchvision version differences"""

    try:
        import torchvision
        torchvision_version = torchvision.__version__

        # Check if we need to apply the fix
        version_parts = torchvision_version.split('.')
        major = int(version_parts[0])
        minor = int(version_parts[1]) if len(version_parts) > 1 else 0

        if major > 0 or minor >= 14:  # torchvision >= 0.14
            # Create a mock functional_tensor module
            import torchvision.transforms.functional as F

            # Create the missing module
            functional_tensor_spec = importlib.util.spec_from_loader(
                'torchvision.transforms.functional_tensor',
                loader=None
            )
            functional_tensor_module = importlib.util.module_from_spec(functional_tensor_spec)

            # Add the missing function
            def rgb_to_grayscale(image, num_output_channels=1):
                """Compatibility function for rgb_to_grayscale"""
                return F.rgb_to_grayscale(image, num_output_channels)

            functional_tensor_module.rgb_to_grayscale = rgb_to_grayscale

            # Register the module
            sys.modules['torchvision.transforms.functional_tensor'] = functional_tensor_module

            print(f"Applied torchvision compatibility fix for version {torchvision_version}")
            return True

    except Exception as e:
        print(f"Failed to apply torchvision compatibility fix: {e}")
        return False

    return False

def patch_basicsr_imports():
    """Patch basicsr imports to handle torchvision compatibility"""
    try:
        # Apply the torchvision fix first
        apply_torchvision_compatibility_fix()

        # Suppress warnings about deprecated modules
        warnings.filterwarnings('ignore', category=UserWarning, module='torchvision')
        warnings.filterwarnings('ignore', category=DeprecationWarning, module='torchvision')

        return True
    except Exception as e:
        print(f"Failed to patch basicsr imports: {e}")
        return False

# Apply the fix when this module is imported
if __name__ != "__main__":
    patch_basicsr_imports()
