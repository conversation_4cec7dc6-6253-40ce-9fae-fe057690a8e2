#!/usr/bin/env python3
"""
修复数字人视频编码问题
确保生成的视频在浏览器中可以正常播放
"""

import os
import subprocess
import glob
from pathlib import Path

def fix_video_encoding_config():
    """修复视频编码配置"""
    
    # 1. 修复 LivePortrait 配置
    liveportrait_config = "backend/storage/models/digital_human/liveportrait/src/config/inference_config.py"
    if os.path.exists(liveportrait_config):
        print(f"修复 LivePortrait 配置: {liveportrait_config}")
        
        # 读取配置文件
        with open(liveportrait_config, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复编码参数
        content = content.replace('crf: int = 15', 'crf: int = 23')  # 降低CRF值，提高兼容性
        content = content.replace('output_fps: int = 25', 'output_fps: int = 30')  # 标准帧率
        
        # 写回文件
        with open(liveportrait_config, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ LivePortrait 配置已修复")
    
    # 2. 修复视频工具配置
    video_utils = "backend/storage/models/digital_human/liveportrait/src/utils/video.py"
    if os.path.exists(video_utils):
        print(f"修复视频工具配置: {video_utils}")
        
        with open(video_utils, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复编码参数，确保浏览器兼容性
        fixes = [
            ("codec = kwargs.get('codec', 'libx264')", "codec = kwargs.get('codec', 'libx264')"),
            ("pixelformat = kwargs.get('pixelformat', 'yuv420p')", "pixelformat = kwargs.get('pixelformat', 'yuv420p')"),
            ("ffmpeg_params = ['-crf', str(kwargs.get('crf', 18))]", 
             "ffmpeg_params = ['-crf', str(kwargs.get('crf', 23)), '-preset', 'medium', '-profile:v', 'baseline', '-level', '3.0']")
        ]
        
        for old, new in fixes:
            content = content.replace(old, new)
        
        with open(video_utils, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 视频工具配置已修复")

def convert_existing_videos():
    """转换现有的视频文件为浏览器兼容格式"""
    
    # 查找所有数字人视频文件
    video_dirs = [
        "backend/storage/digital_humans",
        "frontend/public/storage/digital_humans"
    ]
    
    for video_dir in video_dirs:
        if not os.path.exists(video_dir):
            continue
            
        print(f"处理目录: {video_dir}")
        
        # 查找所有mp4文件
        video_files = glob.glob(os.path.join(video_dir, "**/*.mp4"), recursive=True)
        
        for video_file in video_files:
            print(f"检查视频: {video_file}")
            
            # 检查视频编码
            if not is_browser_compatible(video_file):
                print(f"转换视频: {video_file}")
                convert_to_browser_compatible(video_file)
            else:
                print(f"✅ 视频已兼容: {video_file}")

def is_browser_compatible(video_file):
    """检查视频是否与浏览器兼容"""
    try:
        # 使用ffprobe检查视频信息
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json', 
            '-show_streams', video_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            return False
        
        import json
        info = json.loads(result.stdout)
        
        for stream in info.get('streams', []):
            if stream.get('codec_type') == 'video':
                codec = stream.get('codec_name')
                profile = stream.get('profile', '').lower()
                pixel_format = stream.get('pix_fmt')
                
                # 检查是否为浏览器兼容的编码
                if codec == 'h264' and 'baseline' in profile and pixel_format == 'yuv420p':
                    return True
        
        return False
        
    except Exception as e:
        print(f"检查视频失败: {e}")
        return False

def convert_to_browser_compatible(input_file):
    """转换视频为浏览器兼容格式"""
    try:
        # 创建备份
        backup_file = input_file + '.backup'
        if not os.path.exists(backup_file):
            os.rename(input_file, backup_file)
        
        # 转换命令
        cmd = [
            'ffmpeg', '-i', backup_file,
            '-c:v', 'libx264',           # H.264编码
            '-profile:v', 'baseline',     # 基线配置，最大兼容性
            '-level', '3.0',             # H.264 Level 3.0
            '-pix_fmt', 'yuv420p',       # 像素格式
            '-crf', '23',                # 质量参数
            '-preset', 'medium',         # 编码速度
            '-movflags', '+faststart',   # 优化网络播放
            '-y',                        # 覆盖输出文件
            input_file
        ]
        
        print(f"执行转换命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 转换成功: {input_file}")
            # 删除备份文件
            os.remove(backup_file)
        else:
            print(f"❌ 转换失败: {result.stderr}")
            # 恢复原文件
            os.rename(backup_file, input_file)
            
    except Exception as e:
        print(f"转换视频失败: {e}")

def create_browser_compatible_config():
    """创建浏览器兼容的视频配置文件"""
    
    config_content = '''
# 浏览器兼容的视频编码配置
BROWSER_COMPATIBLE_VIDEO_CONFIG = {
    "codec": "libx264",
    "profile": "baseline",
    "level": "3.0",
    "pixel_format": "yuv420p",
    "crf": 23,
    "preset": "medium",
    "fps": 30,
    "movflags": "+faststart",
    "max_bitrate": "2M",
    "bufsize": "4M"
}

# FFmpeg命令模板
FFMPEG_BROWSER_COMPATIBLE_ARGS = [
    "-c:v", "libx264",
    "-profile:v", "baseline", 
    "-level", "3.0",
    "-pix_fmt", "yuv420p",
    "-crf", "23",
    "-preset", "medium",
    "-movflags", "+faststart",
    "-maxrate", "2M",
    "-bufsize", "4M"
]
'''
    
    config_file = "backend/config/browser_video_config.py"
    os.makedirs(os.path.dirname(config_file), exist_ok=True)
    
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 创建浏览器兼容配置: {config_file}")

def main():
    """主函数"""
    print("🔧 开始修复数字人视频编码问题...")
    
    # 1. 修复配置文件
    fix_video_encoding_config()
    
    # 2. 创建浏览器兼容配置
    create_browser_compatible_config()
    
    # 3. 转换现有视频（可选）
    print("\n是否转换现有视频文件？(y/n): ", end="")
    if input().lower() == 'y':
        convert_existing_videos()
    
    print("\n✅ 视频编码修复完成！")
    print("\n📝 建议:")
    print("1. 重启后端服务以应用新配置")
    print("2. 重新生成数字人视频测试")
    print("3. 检查浏览器中的视频播放效果")

if __name__ == "__main__":
    main()
