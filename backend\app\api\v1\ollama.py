"""
Ollama API路由
提供统一的Ollama服务接口，替代前端直接连接Ollama
"""

from fastapi import APIRouter, HTTPException
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

class OllamaGenerateRequest(BaseModel):
    """Ollama生成请求"""
    model: str
    prompt: str
    system: Optional[str] = None
    temperature: float = 0.7
    max_tokens: int = 2000
    stream: bool = False

class OllamaChatRequest(BaseModel):
    """Ollama聊天请求"""
    model: str
    messages: List[Dict[str, str]]
    temperature: float = 0.7
    max_tokens: int = 2000
    stream: bool = False

class OllamaResponse(BaseModel):
    """Ollama响应"""
    success: bool
    response: Optional[str] = None
    model: Optional[str] = None
    error: Optional[str] = None

@router.get("/models")
async def get_ollama_models():
    """获取Ollama可用模型列表"""
    try:
        from ...services.ollama_service import OllamaService
        ollama_service = OllamaService()
        
        if not ollama_service.available:
            return {
                "success": False,
                "error": "Ollama服务不可用",
                "models": []
            }
        
        models = ollama_service.get_available_models()
        model_list = []
        
        for model in models:
            model_list.append({
                "name": model,
                "id": model,
                "status": "available",
                "type": "ollama"
            })
        
        return {
            "success": True,
            "models": model_list,
            "best_model": ollama_service.best_model
        }
        
    except Exception as e:
        logger.error(f"获取Ollama模型失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "models": []
        }

@router.post("/generate")
async def generate_with_ollama(request: OllamaGenerateRequest):
    """使用Ollama生成文本"""
    try:
        from ...services.ollama_service import OllamaService
        ollama_service = OllamaService()
        
        if not ollama_service.available:
            raise HTTPException(status_code=503, detail="Ollama服务不可用")
        
        response = ollama_service.generate_text(
            model=request.model,
            prompt=request.prompt,
            system_prompt=request.system,
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )
        
        if response is None:
            raise HTTPException(status_code=500, detail="生成失败")
        
        return {
            "success": True,
            "response": response,
            "model": request.model
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ollama生成失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/chat")
async def chat_with_ollama(request: OllamaChatRequest):
    """使用Ollama进行聊天"""
    try:
        from ...services.ollama_service import OllamaService
        ollama_service = OllamaService()
        
        if not ollama_service.available:
            raise HTTPException(status_code=503, detail="Ollama服务不可用")
        
        response = ollama_service.chat_completion(
            model=request.model,
            messages=request.messages,
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )
        
        if response is None:
            raise HTTPException(status_code=500, detail="聊天失败")
        
        return {
            "success": True,
            "response": response,
            "model": request.model
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ollama聊天失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status")
async def get_ollama_status():
    """获取Ollama服务状态"""
    try:
        from ...services.ollama_service import OllamaService
        ollama_service = OllamaService()
        
        return {
            "success": True,
            "available": ollama_service.available,
            "base_url": ollama_service.base_url,
            "best_model": ollama_service.best_model,
            "preferred_models": ollama_service.preferred_models
        }
        
    except Exception as e:
        logger.error(f"获取Ollama状态失败: {e}")
        return {
            "success": False,
            "available": False,
            "error": str(e)
        }
