#!/usr/bin/env python3
"""
GPU加速的MuseTalk推理脚本
使用现有的MuseTalk模型和GPU加速
"""

import os
import sys
import argparse
import logging
import cv2
import numpy as np
import torch
from pathlib import Path
import subprocess
import json
import time

# 设置CUDA内存优化
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

# 设置PyTorch内存管理
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    # 设置内存分配策略
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False

# 添加MuseTalk目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='GPU加速的MuseTalk推理')
    parser.add_argument('--source_image', required=True, help='源图像路径')
    parser.add_argument('--driving_audio', required=True, help='驱动音频路径')
    parser.add_argument('--output', required=True, help='输出视频路径')
    parser.add_argument('--device', default='cuda', choices=['cuda', 'cpu'], help='设备类型')
    parser.add_argument('--fps', type=int, default=25, help='输出视频帧率')
    parser.add_argument('--quality', default='high', choices=['low', 'medium', 'high'], help='输出质量')
    parser.add_argument('--bbox_shift', type=int, default=0, help='边界框偏移')
    return parser.parse_args()

def check_gpu():
    """检查GPU可用性"""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"✅ GPU可用: {gpu_name} ({gpu_memory:.1f}GB)")
        return True
    else:
        logger.warning("⚠️ GPU不可用，将使用CPU")
        return False

def get_audio_duration(audio_path):
    """获取音频时长"""
    try:
        cmd = ['ffprobe', '-v', 'quiet', '-show_entries', 'format=duration', 
               '-of', 'csv=p=0', str(audio_path)]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            duration = float(result.stdout.strip())
            logger.info(f"🎵 音频时长: {duration:.2f}秒")
            return duration
        else:
            logger.warning("无法获取音频时长，使用默认值")
            return 5.0
    except Exception as e:
        logger.warning(f"获取音频时长失败: {e}")
        return 5.0

def run_official_inference(args):
    """运行官方MuseTalk推理"""
    start_time = time.time()
    
    # 检查输入文件
    if not os.path.exists(args.source_image):
        raise FileNotFoundError(f"源图像不存在: {args.source_image}")
    if not os.path.exists(args.driving_audio):
        raise FileNotFoundError(f"音频文件不存在: {args.driving_audio}")
    
    # 获取音频时长
    audio_duration = get_audio_duration(args.driving_audio)
    total_frames = int(audio_duration * args.fps)
    
    logger.info(f"🎬 预计生成帧数: {total_frames}")
    
    # 设置设备
    device = args.device if args.device == 'cpu' or torch.cuda.is_available() else 'cpu'
    logger.info(f"🖥️ 使用设备: {device}")
    
    # 检查模型文件
    model_dir = current_dir / "models"
    if not model_dir.exists():
        raise FileNotFoundError(f"模型目录不存在: {model_dir}")
    
    # 创建临时配置文件
    config = {
        "video_path": str(args.source_image),
        "audio_path": str(args.driving_audio),
        "bbox_shift": args.bbox_shift,
        "fps": args.fps,
        "device": device
    }
    
    config_path = Path(args.output).parent / "temp_musetalk_config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    # 使用真正的MuseTalk推理脚本
    inference_script = current_dir / "scripts" / "inference.py"

    if not inference_script.exists():
        raise FileNotFoundError(f"MuseTalk推理脚本不存在: {inference_script}")

    # 检查可用的模型文件（优先使用V1.5，回退到V1.0）
    unet_model_path = current_dir / "models" / "musetalkV15" / "unet.pth"
    unet_config = current_dir / "models" / "musetalkV15" / "musetalk.json"
    version = "v15"

    if not unet_model_path.exists() or not unet_config.exists():
        logger.warning("⚠️ MuseTalk V1.5模型不完整，回退到V1.0")
        unet_model_path = current_dir / "models" / "musetalk" / "pytorch_model.bin"
        unet_config = current_dir / "models" / "musetalk" / "config.json"
        version = "v1"

        if not unet_model_path.exists():
            raise FileNotFoundError(f"MuseTalk模型不存在: {unet_model_path}")
        if not unet_config.exists():
            raise FileNotFoundError(f"MuseTalk配置不存在: {unet_config}")

    logger.info(f"🎭 使用MuseTalk {version.upper()}模型")
    logger.info(f"📦 模型文件: {unet_model_path.name}")
    logger.info(f"⚙️ 配置文件: {unet_config.name}")

    # 创建临时配置文件
    temp_config = {
        "task_0": {
            "video_path": str(args.source_image),
            "audio_path": str(args.driving_audio),
            "bbox_shift": args.bbox_shift
        }
    }

    import yaml
    config_path = Path(args.output).parent / "temp_inference_config.yaml"
    with open(config_path, 'w') as f:
        yaml.dump(temp_config, f)

    # 构建真正的MuseTalk命令
    cmd = [
        sys.executable, "-m", "scripts.inference",
        "--inference_config", str(config_path),
        "--result_dir", str(Path(args.output).parent),
        "--unet_model_path", str(unet_model_path),
        "--unet_config", str(unet_config),
        "--version", version
    ]

    # 添加GPU参数
    if device == 'cuda':
        cmd.extend(["--gpu_id", "0"])

    logger.info(f"📝 创建临时配置: {config_path}")
    logger.info(f"📝 配置内容: {temp_config}")
    
    logger.info(f"🚀 开始GPU加速MuseTalk推理...")
    logger.info(f"📝 命令: {' '.join(cmd)}")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        # 添加MuseTalk目录到Python路径
        pythonpath = str(current_dir)
        if 'PYTHONPATH' in env:
            pythonpath = f"{pythonpath}{os.pathsep}{env['PYTHONPATH']}"
        env['PYTHONPATH'] = pythonpath

        if device == 'cuda':
            env['CUDA_VISIBLE_DEVICES'] = '0'

        logger.info(f"🐍 PYTHONPATH: {env['PYTHONPATH']}")
        
        # 执行推理
        result = subprocess.run(
            cmd,
            cwd=str(current_dir),
            capture_output=True,
            text=True,
            timeout=300,  # 5分钟超时
            env=env
        )
        
        elapsed_time = time.time() - start_time
        
        if result.returncode == 0:
            logger.info(f"✅ MuseTalk推理成功完成!")
            logger.info(f"⏱️ 总耗时: {elapsed_time:.2f}秒")
            logger.info(f"🎬 平均速度: {total_frames/elapsed_time:.1f} fps")

            # 检查输出文件
            if os.path.exists(args.output):
                file_size = os.path.getsize(args.output)
                logger.info(f"📁 输出文件: {args.output}")
                logger.info(f"📊 文件大小: {file_size/1024:.1f} KB")

                # 检查视频格式是否需要转换
                try:
                    # 检查视频编码
                    check_cmd = [
                        'ffprobe', '-v', 'quiet', '-select_streams', 'v:0',
                        '-show_entries', 'stream=codec_name', '-of', 'csv=p=0',
                        args.output
                    ]

                    check_result = subprocess.run(check_cmd, capture_output=True, text=True)
                    video_codec = check_result.stdout.strip()

                    if video_codec != 'h264':
                        logger.info(f"🔄 检测到视频编码为 {video_codec}，转换为浏览器兼容格式...")
                        temp_output = str(Path(args.output).with_suffix('.temp.mp4'))

                        # 转换为H.264
                        convert_cmd = [
                            'ffmpeg', '-y',
                            '-i', args.output,
                            '-c:v', 'libx264',
                            '-preset', 'fast',
                            '-crf', '23',
                            '-c:a', 'aac',
                            '-movflags', '+faststart',
                            temp_output
                        ]

                        convert_result = subprocess.run(
                            convert_cmd,
                            capture_output=True,
                            text=True,
                            timeout=120
                        )

                        if convert_result.returncode == 0 and Path(temp_output).exists():
                            # 替换原文件
                            Path(args.output).unlink()
                            Path(temp_output).rename(args.output)
                            logger.info("✅ 视频已转换为浏览器兼容格式")
                        else:
                            logger.warning(f"⚠️ 视频转换失败: {convert_result.stderr}")
                    else:
                        logger.info("✅ 视频已是H.264格式，无需转换")

                except Exception as e:
                    logger.warning(f"视频格式检查失败: {e}")

                return True
            else:
                # MuseTalk将输出保存在result_dir/version/子目录中
                # 需要在这些位置查找生成的视频文件
                output_dir = Path(args.output).parent
                output_name = Path(args.output).name

                # 构建可能的输出路径
                possible_paths = [
                    # 直接在输出目录中
                    output_dir / output_name,
                    # 在v15子目录中
                    output_dir / "v15" / output_name,
                    # 在musetalkV15子目录中
                    output_dir / "musetalkV15" / output_name,
                    # 使用输入文件名构建的输出名称
                    output_dir / "v15" / f"{Path(args.source_image).stem}_{Path(args.driving_audio).stem}.mp4"
                ]

                # 查找所有可能的输出文件
                found_output = None
                for possible_path in possible_paths:
                    if possible_path.exists():
                        found_output = possible_path
                        break

                # 如果还没找到，搜索所有mp4文件（但要避免重用旧文件）
                if not found_output:
                    current_time = time.time()
                    for search_dir in [output_dir, output_dir / "v15", output_dir / "musetalkV15"]:
                        if search_dir.exists():
                            mp4_files = list(search_dir.glob("*.mp4"))
                            if mp4_files:
                                # 只选择最近5分钟内创建的文件，避免重用旧文件
                                recent_files = [f for f in mp4_files if (current_time - f.stat().st_mtime) < 300]
                                if recent_files:
                                    # 选择最新的最近文件
                                    found_output = max(recent_files, key=lambda p: p.stat().st_mtime)
                                    logger.info(f"📁 找到最近生成的文件: {found_output}")
                                    break
                                else:
                                    logger.warning(f"⚠️ 目录 {search_dir} 中的mp4文件都太旧，跳过")

                if found_output:
                    logger.info(f"📁 找到输出文件: {found_output}")
                    file_size = found_output.stat().st_size
                    logger.info(f"📊 文件大小: {file_size/1024:.1f} KB")

                    # 转换为浏览器兼容格式
                    try:
                        logger.info("🔄 转换视频为浏览器兼容格式...")
                        temp_output = str(Path(args.output).with_suffix('.temp.mp4'))

                        # 使用ffmpeg转换为H.264编码
                        convert_cmd = [
                            'ffmpeg', '-y',
                            '-i', str(found_output),
                            '-c:v', 'libx264',  # H.264视频编码
                            '-preset', 'fast',   # 快速编码
                            '-crf', '23',        # 质量设置
                            '-c:a', 'aac',       # AAC音频编码
                            '-movflags', '+faststart',  # 优化网络播放
                            temp_output
                        ]

                        convert_result = subprocess.run(
                            convert_cmd,
                            capture_output=True,
                            text=True,
                            timeout=120  # 2分钟转换超时
                        )

                        if convert_result.returncode == 0 and Path(temp_output).exists():
                            # 转换成功，替换原文件
                            Path(temp_output).rename(args.output)
                            logger.info(f"✅ 视频已转换为浏览器兼容格式: {args.output}")

                            # 删除原始文件
                            if found_output != Path(args.output):
                                found_output.unlink()

                            return True
                        else:
                            logger.warning(f"⚠️ 视频转换失败，使用原始文件: {convert_result.stderr}")
                            # 转换失败，使用原始文件
                            found_output.rename(args.output)
                            logger.info(f"📁 已移动原始文件到: {args.output}")
                            return True

                    except Exception as e:
                        logger.warning(f"视频转换异常: {e}")
                        # 转换失败，尝试直接移动文件
                        try:
                            found_output.rename(args.output)
                            logger.info(f"📁 已移动到: {args.output}")
                            return True
                        except Exception as e2:
                            logger.warning(f"移动文件失败: {e2}")
                            # 如果移动失败，至少复制文件
                            try:
                                import shutil
                                shutil.copy2(found_output, args.output)
                                logger.info(f"📁 已复制到: {args.output}")
                                return True
                            except Exception as e3:
                                logger.error(f"复制文件也失败: {e3}")
                else:
                    logger.warning("⚠️ 未找到输出文件")
                    # 列出输出目录的内容以便调试
                    logger.info(f"输出目录内容: {list(output_dir.iterdir()) if output_dir.exists() else '目录不存在'}")
                    if (output_dir / "v15").exists():
                        logger.info(f"v15子目录内容: {list((output_dir / 'v15').iterdir())}")

            return False
        else:
            logger.error(f"❌ MuseTalk推理失败")
            logger.error(f"标准输出: {result.stdout}")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ 推理超时")
        return False
    except Exception as e:
        logger.error(f"❌ 推理过程中出错: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            if config_path.exists():
                config_path.unlink()
                logger.info("🧹 清理临时配置文件")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")

def main():
    args = parse_args()
    
    logger.info("🎭 GPU加速MuseTalk推理开始...")
    logger.info(f"源图像: {args.source_image}")
    logger.info(f"驱动音频: {args.driving_audio}")
    logger.info(f"输出视频: {args.output}")
    logger.info(f"设备: {args.device}")
    logger.info(f"质量: {args.quality}")
    
    # 检查GPU
    if args.device == 'cuda':
        check_gpu()
    
    try:
        # 运行推理
        success = run_official_inference(args)
        
        if success:
            logger.info("🎉 GPU加速MuseTalk推理完成！")
            sys.exit(0)
        else:
            logger.error("💥 MuseTalk推理失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
