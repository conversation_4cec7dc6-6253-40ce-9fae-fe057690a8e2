/**
 * 动态 API 配置 - 自动适配 localhost 和 IP 地址访问
 */

// 获取当前页面的主机信息
const getCurrentHost = () => {
  const { hostname, port, protocol } = window.location;
  return { hostname, port, protocol };
};

// 检测可用的 API 服务器
const detectApiServer = async () => {
  const { hostname, protocol } = getCurrentHost();
  
  // 候选的 API 服务器地址
  const candidates = [
    // 优先使用已知的后端服务器地址
    `${protocol}//*************:8000`,
    // 如果当前是 IP 访问，尝试使用相同 IP 的后端
    hostname !== 'localhost' && hostname !== '127.0.0.1' ? `${protocol}//${hostname}:8000` : null,
    // 本地回环地址
    `${protocol}//localhost:8000`,
    `${protocol}//127.0.0.1:8000`,
  ].filter(Boolean);

  console.log('[API配置] 检测API服务器，候选地址:', candidates);

  // 测试每个候选地址
  for (const baseUrl of candidates) {
    try {
      console.log(`[API配置] 测试API服务器: ${baseUrl}`);
      
      const response = await fetch(`${baseUrl}/health`, {
        method: 'GET',
        timeout: 3000,
        signal: AbortSignal.timeout(3000)
      });
      
      if (response.ok) {
        console.log(`[API配置] ✅ 找到可用的API服务器: ${baseUrl}`);
        return baseUrl;
      }
    } catch (error) {
      console.log(`[API配置] ❌ API服务器不可用: ${baseUrl} - ${error.message}`);
    }
  }

  // 如果都失败了，使用默认地址
  const defaultUrl = `${protocol}//${hostname}:8000`;
  console.warn(`[API配置] ⚠️ 所有API服务器都不可用，使用默认地址: ${defaultUrl}`);
  return defaultUrl;
};

// 缓存检测结果
let cachedApiBaseUrl = null;
let detectionPromise = null;

// 获取 API 基础 URL
export const getApiBaseUrl = async () => {
  if (cachedApiBaseUrl) {
    return cachedApiBaseUrl;
  }

  if (!detectionPromise) {
    detectionPromise = detectApiServer();
  }

  cachedApiBaseUrl = await detectionPromise;
  return cachedApiBaseUrl;
};

// 同步获取 API 基础 URL（使用缓存值或默认值）
export const getApiBaseUrlSync = () => {
  if (cachedApiBaseUrl) {
    return cachedApiBaseUrl;
  }

  // 如果没有缓存，使用当前主机的默认配置
  const { hostname, protocol } = getCurrentHost();
  const defaultUrl = `${protocol}//${hostname}:8000`;
  
  console.log(`[API配置] 使用同步默认URL: ${defaultUrl}`);
  return defaultUrl;
};

// 获取 WebSocket URL
export const getWebSocketUrl = async () => {
  const apiBaseUrl = await getApiBaseUrl();
  const wsProtocol = apiBaseUrl.startsWith('https:') ? 'wss:' : 'ws:';
  const wsUrl = apiBaseUrl.replace(/^https?:/, wsProtocol);
  
  console.log(`[API配置] WebSocket URL: ${wsUrl}`);
  return wsUrl;
};

// 创建完整的 API URL
export const createApiUrl = async (path) => {
  const baseUrl = await getApiBaseUrl();
  const fullUrl = `${baseUrl}${path.startsWith('/') ? path : '/' + path}`;
  
  console.log(`[API配置] 创建API URL: ${path} -> ${fullUrl}`);
  return fullUrl;
};

// 重置缓存（用于重新检测）
export const resetApiConfig = () => {
  cachedApiBaseUrl = null;
  detectionPromise = null;
  console.log('[API配置] 已重置缓存，下次调用将重新检测');
};

// 初始化检测（页面加载时）
export const initApiConfig = async () => {
  console.log('[API配置] 初始化API配置...');
  
  try {
    const baseUrl = await getApiBaseUrl();
    console.log(`[API配置] ✅ 初始化完成，API服务器: ${baseUrl}`);
    return baseUrl;
  } catch (error) {
    console.error('[API配置] ❌ 初始化失败:', error);
    throw error;
  }
};

// 导出默认配置对象
export default {
  getApiBaseUrl,
  getApiBaseUrlSync,
  getWebSocketUrl,
  createApiUrl,
  resetApiConfig,
  initApiConfig,
  getCurrentHost
};
