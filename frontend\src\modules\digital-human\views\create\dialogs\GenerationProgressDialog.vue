<template>
  <el-dialog
    v-model="visible"
    title="数字人生成中"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    center
  >
    <div class="generation-progress">
      <!-- 进度环 -->
      <div class="progress-circle">
        <el-progress
          type="circle"
          :percentage="progress"
          :width="120"
          :stroke-width="8"
          :color="progressColor"
        >
          <template #default="{ percentage }">
            <div class="progress-content">
              <el-icon class="progress-icon" :color="progressColor">
                <component :is="statusIcon" />
              </el-icon>
              <div class="progress-text">{{ percentage }}%</div>
            </div>
          </template>
        </el-progress>
      </div>
      
      <!-- 状态信息 -->
      <div class="status-info">
        <h3 class="status-title">{{ statusMessage }}</h3>
        <p class="status-description">{{ statusDescription }}</p>
      </div>
      
      <!-- 处理步骤 -->
      <div class="processing-steps">
        <div 
          v-for="(step, index) in processingSteps" 
          :key="index"
          class="step-item"
          :class="{ 
            'step-completed': step.completed,
            'step-current': step.current,
            'step-pending': !step.completed && !step.current
          }"
        >
          <div class="step-icon">
            <el-icon v-if="step.completed">
              <Check />
            </el-icon>
            <el-icon v-else-if="step.current" class="rotating">
              <Loading />
            </el-icon>
            <div v-else class="step-number">{{ index + 1 }}</div>
          </div>
          <div class="step-content">
            <div class="step-title">{{ step.title }}</div>
            <div class="step-description">{{ step.description }}</div>
          </div>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div v-if="errorMessage" class="error-info">
        <el-alert
          :title="errorMessage"
          type="error"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 生成完成后的预览 -->
      <div v-if="status === 'completed' && digitalHumanId" class="completion-preview">
        <div class="preview-header">
          <h4>🎉 生成完成！</h4>
          <p>您的数字人已经准备就绪</p>
        </div>

        <div class="preview-content">
          <!-- 视频预览 -->
          <div class="video-preview">
            <h5>📹 说话视频预览</h5>
            <div class="video-container">
              <video
                v-if="previewVideoUrl"
                :src="previewVideoUrl"
                controls
                preload="metadata"
                class="preview-video"
                @error="handleVideoError"
              >
                您的浏览器不支持视频播放
              </video>
              <div v-else class="video-placeholder">
                <el-icon><VideoPlay /></el-icon>
                <span>视频加载中...</span>
              </div>
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="quick-actions">
            <el-button
              size="small"
              @click="downloadVideo"
              :disabled="!previewVideoUrl"
            >
              <el-icon><Download /></el-icon>
              下载视频
            </el-button>
            <el-button
              size="small"
              @click="shareVideo"
              :disabled="!previewVideoUrl"
            >
              <el-icon><Share /></el-icon>
              分享
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button 
          v-if="status === 'failed'"
          type="primary" 
          @click="retry"
        >
          重试
        </el-button>
        <el-button 
          v-if="status === 'completed'"
          type="primary" 
          @click="viewResult"
        >
          查看结果
        </el-button>
        <el-button 
          v-if="status === 'failed' || status === 'completed'"
          @click="close"
        >
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue';
import {
  Check,
  Loading,
  VideoPlay,
  Microphone,
  Setting,
  Picture,
  Download,
  Share
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { 
  formatProgressText,
  getStatusIcon,
  STATUS_COLOR,
  GENERATION_STATUS
} from '../api/digitalHumanGeneration.js';

export default defineComponent({
  name: 'GenerationProgressDialog',
  
  components: {
    Check,
    Loading,
    VideoPlay,
    Microphone,
    Setting,
    Picture
  },
  
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    status: {
      type: String,
      default: 'pending'
    },
    progress: {
      type: Number,
      default: 0
    },
    message: {
      type: String,
      default: ''
    },
    errorMessage: {
      type: String,
      default: ''
    },
    digitalHumanId: {
      type: Number,
      default: null
    }
  },
  
  emits: ['update:modelValue', 'retry', 'view-result', 'close'],
  
  setup(props, { emit }) {
    const visible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    });
    
    // 状态图标
    const statusIcon = computed(() => {
      return getStatusIcon(props.status);
    });
    
    // 进度颜色
    const progressColor = computed(() => {
      return STATUS_COLOR[props.status] || '#409EFF';
    });
    
    // 状态消息
    const statusMessage = computed(() => {
      return formatProgressText(props.status, props.progress);
    });
    
    // 状态描述
    const statusDescription = computed(() => {
      const descriptions = {
        [GENERATION_STATUS.PENDING]: '正在准备生成环境...',
        [GENERATION_STATUS.RUNNING]: '正在使用AI技术生成您的数字人...',
        [GENERATION_STATUS.COMPLETED]: '您的数字人已成功生成！',
        [GENERATION_STATUS.FAILED]: '生成过程中遇到了问题，请重试'
      };
      
      return props.message || descriptions[props.status] || '';
    });
    
    // 处理步骤
    const processingSteps = computed(() => {
      const steps = [
        {
          title: '生成头像图片',
          description: '基于您的设置生成个性化头像',
          icon: Picture
        },
        {
          title: '生成头像视频',
          description: '创建会说话的动态头像',
          icon: VideoPlay
        },
        {
          title: '生成声音样本',
          description: '合成专属的数字人声音',
          icon: Microphone
        },
        {
          title: '配置数字人模型',
          description: '完成最终的模型配置',
          icon: Setting
        }
      ];
      
      return steps.map((step, index) => {
        const stepProgress = (index + 1) * 25;
        return {
          ...step,
          completed: props.progress > stepProgress,
          current: props.progress >= stepProgress - 25 && props.progress <= stepProgress && props.status === 'running'
        };
      });
    });
    
    // 重试
    const retry = () => {
      emit('retry');
    };
    
    // 查看结果
    const viewResult = () => {
      emit('view-result', props.digitalHumanId);
    };
    
    // 关闭
    const close = () => {
      emit('close');
    };

    // 预览视频URL
    const previewVideoUrl = computed(() => {
      if (props.digitalHumanId && props.status === 'completed') {
        // 尝试多种可能的文件名格式
        const today = new Date();
        const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
        const timeStr = today.toTimeString().slice(0, 8).replace(/:/g, '.');

        // 可能的文件名格式
        const possibleNames = [
          `preview_${props.digitalHumanId}_${dateStr}${timeStr.replace(/\./g, '')}.mp4`,
          `preview_${props.digitalHumanId}_${dateStr}.mp4`,
          `${today.getFullYear()}_${String(today.getMonth() + 1).padStart(2, '0')}_${String(today.getDate()).padStart(2, '0')}_${timeStr}.mp4`
        ];

        // 返回第一个可能的文件名，实际应该通过API获取
        return `/storage/digital_humans/preview_${props.digitalHumanId}_${dateStr}.mp4`;
      }
      return null;
    });

    // 视频错误处理
    const handleVideoError = (event) => {
      console.error('视频加载失败:', event);
      ElMessage.warning('视频加载失败，请稍后重试');
    };

    // 下载视频
    const downloadVideo = () => {
      if (previewVideoUrl.value) {
        const link = document.createElement('a');
        link.href = previewVideoUrl.value;
        link.download = `数字人视频_${props.digitalHumanId}.mp4`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        ElMessage.success('视频下载已开始');
      }
    };

    // 分享视频
    const shareVideo = () => {
      if (previewVideoUrl.value) {
        const fullUrl = `${window.location.origin}${previewVideoUrl.value}`;
        if (navigator.share) {
          navigator.share({
            title: '我的数字人视频',
            text: '看看我创建的数字人！',
            url: fullUrl
          }).catch(console.error);
        } else {
          navigator.clipboard.writeText(fullUrl).then(() => {
            ElMessage.success('视频链接已复制到剪贴板');
          }).catch(() => {
            ElMessage.error('复制失败');
          });
        }
      }
    };
    
    return {
      visible,
      statusIcon,
      progressColor,
      statusMessage,
      statusDescription,
      processingSteps,
      retry,
      viewResult,
      close,
      previewVideoUrl,
      handleVideoError,
      downloadVideo,
      shareVideo
    };
  }
});
</script>

<style scoped>
.generation-progress {
  text-align: center;
  padding: 20px;
}

.progress-circle {
  margin-bottom: 30px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.progress-icon {
  font-size: 24px;
}

.progress-text {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.status-info {
  margin-bottom: 30px;
}

.status-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.status-description {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.processing-steps {
  text-align: left;
  margin-bottom: 20px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.step-item:last-child {
  border-bottom: none;
}

.step-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.step-completed .step-icon {
  background: #67c23a;
  color: white;
}

.step-current .step-icon {
  background: #409eff;
  color: white;
}

.step-pending .step-icon {
  background: #f0f0f0;
  color: #909399;
}

.step-number {
  font-size: 12px;
  font-weight: 600;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.step-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.step-completed .step-title {
  color: #67c23a;
}

.step-current .step-title {
  color: #409eff;
}

.error-info {
  margin-top: 20px;
}

.dialog-footer {
  text-align: center;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 完成预览样式 */
.completion-preview {
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 12px;
  border: 1px solid #0ea5e9;
}

.preview-header {
  text-align: center;
  margin-bottom: 20px;
}

.preview-header h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #0369a1;
}

.preview-header p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.video-preview h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.video-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
}

.preview-video {
  width: 100%;
  height: auto;
  max-height: 200px;
  display: block;
}

.video-placeholder {
  aspect-ratio: 16/9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  color: #6b7280;
  font-size: 14px;
  gap: 8px;
}

.video-placeholder .el-icon {
  font-size: 32px;
}

.quick-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.quick-actions .el-button {
  font-size: 12px;
}
</style>
